import { User } from "@/types";
import AuthService from "./auth";
import EncryptionService from "./encryption";

interface CreateUserRequest {
  email: string;
  password: string;
  name: string;
  role: "admin" | "user";
}

interface UpdateUserRequest {
  name?: string;
  email?: string;
  role?: "admin" | "user";
  status?: "active" | "inactive";
}

interface UserWithStatus extends User {
  status: "active" | "inactive";
}

class UserManagementService {
  private static instance: UserManagementService;
  private readonly API_BASE_URL =
    import.meta.env.VITE_API_BASE_URL || "http://localhost:3001";

  static getInstance(): UserManagementService {
    if (!UserManagementService.instance) {
      UserManagementService.instance = new UserManagementService();
    }
    return UserManagementService.instance;
  }

  async getUsers(): Promise<UserWithStatus[]> {
    try {
      AuthService.requireAdmin();

      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/users`,
      );

      if (response.ok) {
        const data = await response.json();
        return data.users || [];
      } else {
        throw new Error("Failed to fetch users");
      }
    } catch (error) {
      console.error("Error fetching users:", error);
      throw error;
    }
  }

  async createUser(userData: CreateUserRequest): Promise<UserWithStatus> {
    try {
      AuthService.requireAdmin();

      // Hash password before sending
      const hashedPassword = EncryptionService.hash(userData.password);

      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/users`,
        {
          method: "POST",
          body: JSON.stringify({
            ...userData,
            password: hashedPassword,
          }),
        },
      );

      if (response.ok) {
        const newUser = await response.json();
        return newUser as unknown as UserWithStatus;
      } else {
        const error = await response.json();
        throw new Error(error.message || "Failed to create user");
      }
    } catch (error) {
      console.error("Error creating user:", error);
      throw error;
    }
  }

  async updateUser(
    userId: string,
    updates: UpdateUserRequest,
  ): Promise<UserWithStatus> {
    try {
      AuthService.requireAdmin();

      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/users/${userId}`,
        {
          method: "PUT",
          body: JSON.stringify(updates),
        },
      );

      if (response.ok) {
        const updatedUser = await response.json();
        return updatedUser as UserWithStatus;
      } else {
        const error = await response.json();
        throw new Error(error.message || "Failed to update user");
      }
    } catch (error) {
      console.error("Error updating user:", error);
      throw error;
    }
  }

  async deleteUser(userId: string): Promise<void> {
    try {
      AuthService.requireAdmin();

      const currentUser = AuthService.getUser();
      if (currentUser?.id === userId) {
        throw new Error("Cannot delete your own account");
      }

      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/users/${userId}`,
        {
          method: "DELETE",
        },
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to delete user");
      }
    } catch (error) {
      console.error("Error deleting user:", error);
      throw error;
    }
  }

  async toggleUserStatus(userId: string): Promise<UserWithStatus> {
    try {
      AuthService.requireAdmin();

      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/users/${userId}/toggle-status`,
        {
          method: "PATCH",
        },
      );

      if (response.ok) {
        const updatedUser = await response.json();
        return updatedUser as UserWithStatus;
      } else {
        const error = await response.json();
        throw new Error(error.message || "Failed to toggle user status");
      }
    } catch (error) {
      console.error("Error toggling user status:", error);
      throw error;
    }
  }

  async changePassword(userId: string, newPassword: string): Promise<void> {
    try {
      AuthService.requireAdmin();

      const hashedPassword = EncryptionService.hash(newPassword);

      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/users/${userId}/password`,
        {
          method: "PATCH",
          body: JSON.stringify({ password: hashedPassword }),
        },
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to change password");
      }
    } catch (error) {
      console.error("Error changing password:", error);
      throw error;
    }
  }

  async getUserPermissions(userId: string): Promise<string[]> {
    try {
      AuthService.requireAdmin();

      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/users/${userId}/permissions`,
      );

      if (response.ok) {
        const data = await response.json();
        return data.permissions || [];
      } else {
        throw new Error("Failed to fetch user permissions");
      }
    } catch (error) {
      console.error("Error fetching user permissions:", error);
      throw error;
    }
  }

  async updateUserPermissions(
    userId: string,
    permissions: string[],
  ): Promise<void> {
    try {
      AuthService.requireAdmin();

      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/users/${userId}/permissions`,
        {
          method: "PUT",
          body: JSON.stringify({ permissions }),
        },
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to update user permissions");
      }
    } catch (error) {
      console.error("Error updating user permissions:", error);
      throw error;
    }
  }
}

export default UserManagementService.getInstance();
