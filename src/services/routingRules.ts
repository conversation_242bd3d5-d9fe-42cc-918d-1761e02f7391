import { RoutingRule } from "@/types";
import AuthService from "./auth";

interface RuleCondition {
  type: "contains" | "regex" | "length" | "sentiment" | "language" | "default";
  value?: string | number;
  options?: string[];
}

class RoutingRulesService {
  private static instance: RoutingRulesService;
  private rules: RoutingRule[] = [];
  private readonly API_BASE_URL =
    import.meta.env.VITE_API_BASE_URL || "http://localhost:3001";

  static getInstance(): RoutingRulesService {
    if (!RoutingRulesService.instance) {
      RoutingRulesService.instance = new RoutingRulesService();
    }
    return RoutingRulesService.instance;
  }

  async loadRules(): Promise<void> {
    try {
      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/routing-rules`,
      );

      if (response.ok) {
        const data = await response.json();
        this.rules = data.rules || [];
        this.sortRulesByPriority();
      }
    } catch (error) {
      console.error("Failed to load routing rules:", error);
      // Use default rules if loading fails
      this.initializeDefaultRules();
    }
  }

  private initializeDefaultRules(): void {
    this.rules = [
      {
        id: "default",
        name: "Default Route",
        priority: 999,
        condition: "default",
        provider: "openai",
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];
  }

  private sortRulesByPriority(): void {
    this.rules.sort((a, b) => a.priority - b.priority);
  }

  async createRule(
    rule: Omit<RoutingRule, "id" | "createdAt" | "updatedAt">,
  ): Promise<RoutingRule> {
    try {
      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/routing-rules`,
        {
          method: "POST",
          body: JSON.stringify(rule),
        },
      );

      if (response.ok) {
        const newRule = await response.json();
        this.rules.push(newRule);
        this.sortRulesByPriority();
        return newRule;
      } else {
        throw new Error("Failed to create routing rule");
      }
    } catch (error) {
      console.error("Error creating routing rule:", error);
      throw error;
    }
  }

  async updateRule(
    id: string,
    updates: Partial<RoutingRule>,
  ): Promise<RoutingRule> {
    try {
      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/routing-rules/${id}`,
        {
          method: "PUT",
          body: JSON.stringify(updates),
        },
      );

      if (response.ok) {
        const updatedRule = await response.json();
        const index = this.rules.findIndex((rule) => rule.id === id);
        if (index !== -1) {
          this.rules[index] = updatedRule;
          this.sortRulesByPriority();
        }
        return updatedRule;
      } else {
        throw new Error("Failed to update routing rule");
      }
    } catch (error) {
      console.error("Error updating routing rule:", error);
      throw error;
    }
  }

  async deleteRule(id: string): Promise<void> {
    try {
      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/routing-rules/${id}`,
        {
          method: "DELETE",
        },
      );

      if (response.ok) {
        this.rules = this.rules.filter((rule) => rule.id !== id);
      } else {
        throw new Error("Failed to delete routing rule");
      }
    } catch (error) {
      console.error("Error deleting routing rule:", error);
      throw error;
    }
  }

  getRules(): RoutingRule[] {
    return [...this.rules];
  }

  getActiveRules(): RoutingRule[] {
    return this.rules.filter((rule) => rule.isActive);
  }

  evaluateMessage(message: string): string {
    const activeRules = this.getActiveRules();

    for (const rule of activeRules) {
      if (this.evaluateCondition(rule.condition, message)) {
        return rule.provider;
      }
    }

    // Fallback to default provider
    const defaultRule = activeRules.find(
      (rule) => rule.condition === "default",
    );
    return defaultRule?.provider || "openai";
  }

  private evaluateCondition(condition: string, message: string): boolean {
    try {
      // Parse condition string
      const parsedCondition = this.parseCondition(condition);

      switch (parsedCondition.type) {
        case "contains":
          return this.evaluateContains(parsedCondition, message);
        case "regex":
          return this.evaluateRegex(parsedCondition, message);
        case "length":
          return this.evaluateLength(parsedCondition, message);
        case "sentiment":
          return this.evaluateSentiment(parsedCondition, message);
        case "language":
          return this.evaluateLanguage(parsedCondition, message);
        case "default":
          return true; // Default rule always matches
        default:
          return false;
      }
    } catch (error) {
      console.error("Error evaluating condition:", error);
      return false;
    }
  }

  private parseCondition(condition: string): RuleCondition {
    // Handle default condition
    if (condition === "default") {
      return { type: "default" };
    }

    // Parse contains() function
    const containsMatch = condition.match(/contains\((.+)\)/);
    if (containsMatch) {
      const keywords = containsMatch[1]
        .split(",")
        .map((k) => k.trim().replace(/["']/g, ""));
      return { type: "contains", options: keywords };
    }

    // Parse regex() function
    const regexMatch = condition.match(/regex\((.+)\)/);
    if (regexMatch) {
      return { type: "regex", value: regexMatch[1].replace(/["']/g, "") };
    }

    // Parse length conditions
    const lengthMatch = condition.match(/(length|len)\s*([><=]+)\s*(\d+)/);
    if (lengthMatch) {
      const operator = lengthMatch[2];
      const value = parseInt(lengthMatch[3]);
      return { type: "length", value, options: [operator] };
    }

    // Default to contains for simple string conditions
    return { type: "contains", options: [condition] };
  }

  private evaluateContains(condition: RuleCondition, message: string): boolean {
    if (!condition.options) return false;

    const lowerMessage = message.toLowerCase();
    return condition.options.some((keyword) =>
      lowerMessage.includes(keyword.toLowerCase()),
    );
  }

  private evaluateRegex(condition: RuleCondition, message: string): boolean {
    if (!condition.value) return false;

    try {
      const regex = new RegExp(condition.value as string, "i");
      return regex.test(message);
    } catch (error) {
      console.error("Invalid regex pattern:", condition.value);
      return false;
    }
  }

  private evaluateLength(condition: RuleCondition, message: string): boolean {
    if (!condition.value || !condition.options) return false;

    const messageLength = message.length;
    const targetLength = condition.value as number;
    const operator = condition.options[0];

    switch (operator) {
      case ">":
        return messageLength > targetLength;
      case "<":
        return messageLength < targetLength;
      case ">=":
        return messageLength >= targetLength;
      case "<=":
        return messageLength <= targetLength;
      case "==":
      case "=":
        return messageLength === targetLength;
      default:
        return false;
    }
  }

  private evaluateSentiment(
    condition: RuleCondition,
    message: string,
  ): boolean {
    // Simple sentiment analysis - in production, use a proper sentiment analysis library
    const positiveWords = [
      "good",
      "great",
      "excellent",
      "amazing",
      "wonderful",
      "fantastic",
      "love",
      "like",
    ];
    const negativeWords = [
      "bad",
      "terrible",
      "awful",
      "hate",
      "dislike",
      "horrible",
      "worst",
      "sucks",
    ];

    const lowerMessage = message.toLowerCase();
    const positiveCount = positiveWords.filter((word) =>
      lowerMessage.includes(word),
    ).length;
    const negativeCount = negativeWords.filter((word) =>
      lowerMessage.includes(word),
    ).length;

    const sentiment =
      positiveCount > negativeCount
        ? "positive"
        : negativeCount > positiveCount
          ? "negative"
          : "neutral";

    return condition.value === sentiment;
  }

  private evaluateLanguage(condition: RuleCondition, message: string): boolean {
    // Simple language detection - in production, use a proper language detection library
    const patterns = {
      english: /^[a-zA-Z\s.,!?;:()"'-]+$/,
      spanish: /[ñáéíóúü]/i,
      french: /[àâäéèêëïîôöùûüÿç]/i,
      german: /[äöüß]/i,
    };

    const targetLanguage = condition.value as string;
    const pattern = patterns[targetLanguage as keyof typeof patterns];

    return pattern ? pattern.test(message) : false;
  }

  async testRule(
    ruleId: string,
    testMessage: string,
  ): Promise<{ matches: boolean; provider: string }> {
    const rule = this.rules.find((r) => r.id === ruleId);
    if (!rule) {
      throw new Error("Rule not found");
    }

    const matches = this.evaluateCondition(rule.condition, testMessage);
    return {
      matches,
      provider: matches ? rule.provider : "none",
    };
  }
}

export default RoutingRulesService.getInstance();
