import { SystemPrompt } from "@/types";
import AuthService from "./auth";

interface CreateSystemPromptRequest {
  name: string;
  content: string;
  context: "general" | "technical" | "creative" | "support" | "custom";
}

interface UpdateSystemPromptRequest {
  name?: string;
  content?: string;
  context?: "general" | "technical" | "creative" | "support" | "custom";
  isActive?: boolean;
}

class SystemPromptsService {
  private static instance: SystemPromptsService;
  private readonly API_BASE_URL =
    import.meta.env.VITE_API_BASE_URL || "http://localhost:3001";

  static getInstance(): SystemPromptsService {
    if (!SystemPromptsService.instance) {
      SystemPromptsService.instance = new SystemPromptsService();
    }
    return SystemPromptsService.instance;
  }

  async getSystemPrompts(): Promise<SystemPrompt[]> {
    try {
      AuthService.requireAdmin();

      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/system-prompts`,
      );

      if (response.ok) {
        const data = await response.json();
        return data.prompts || [];
      } else {
        throw new Error("Failed to fetch system prompts");
      }
    } catch (error) {
      console.error("Error fetching system prompts:", error);
      throw error;
    }
  }

  async createSystemPrompt(
    promptData: CreateSystemPromptRequest,
  ): Promise<SystemPrompt> {
    try {
      AuthService.requireAdmin();

      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/system-prompts`,
        {
          method: "POST",
          body: JSON.stringify(promptData),
        },
      );

      if (response.ok) {
        const newPrompt = await response.json();
        return newPrompt;
      } else {
        const error = await response.json();
        throw new Error(error.message || "Failed to create system prompt");
      }
    } catch (error) {
      console.error("Error creating system prompt:", error);
      throw error;
    }
  }

  async updateSystemPrompt(
    promptId: string,
    updates: UpdateSystemPromptRequest,
  ): Promise<SystemPrompt> {
    try {
      AuthService.requireAdmin();

      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/system-prompts/${promptId}`,
        {
          method: "PUT",
          body: JSON.stringify(updates),
        },
      );

      if (response.ok) {
        const updatedPrompt = await response.json();
        return updatedPrompt;
      } else {
        const error = await response.json();
        throw new Error(error.message || "Failed to update system prompt");
      }
    } catch (error) {
      console.error("Error updating system prompt:", error);
      throw error;
    }
  }

  async deleteSystemPrompt(promptId: string): Promise<void> {
    try {
      AuthService.requireAdmin();

      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/system-prompts/${promptId}`,
        {
          method: "DELETE",
        },
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to delete system prompt");
      }
    } catch (error) {
      console.error("Error deleting system prompt:", error);
      throw error;
    }
  }

  async toggleSystemPromptStatus(promptId: string): Promise<SystemPrompt> {
    try {
      AuthService.requireAdmin();

      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/system-prompts/${promptId}/toggle-status`,
        {
          method: "PATCH",
        },
      );

      if (response.ok) {
        const updatedPrompt = await response.json();
        return updatedPrompt;
      } else {
        const error = await response.json();
        throw new Error(
          error.message || "Failed to toggle system prompt status",
        );
      }
    } catch (error) {
      console.error("Error toggling system prompt status:", error);
      throw error;
    }
  }

  async getActiveSystemPrompt(
    context: string = "general",
  ): Promise<SystemPrompt | null> {
    try {
      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/system-prompts/active?context=${context}`,
      );

      if (response.ok) {
        const data = await response.json();
        return data.prompt || null;
      } else {
        return null;
      }
    } catch (error) {
      console.error("Error fetching active system prompt:", error);
      return null;
    }
  }

  async testSystemPrompt(
    promptId: string,
    testMessage: string,
  ): Promise<{ success: boolean; response: string; error?: string }> {
    try {
      AuthService.requireAdmin();

      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/system-prompts/${promptId}/test`,
        {
          method: "POST",
          body: JSON.stringify({ testMessage }),
        },
      );

      if (response.ok) {
        const result = await response.json();
        return result;
      } else {
        const error = await response.json();
        return {
          success: false,
          response: "",
          error: error.message || "Test failed",
        };
      }
    } catch (error: any) {
      console.error("Error testing system prompt:", error);
      return {
        success: false,
        response: "",
        error: error.message || "Network error during test",
      };
    }
  }
}

export default SystemPromptsService.getInstance();
