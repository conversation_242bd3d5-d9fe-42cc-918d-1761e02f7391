import CryptoJS from "crypto-js";

class EncryptionService {
  private static instance: EncryptionService;
  private readonly secretKey: string;

  static getInstance(): EncryptionService {
    if (!EncryptionService.instance) {
      EncryptionService.instance = new EncryptionService();
    }
    return EncryptionService.instance;
  }

  constructor() {
    // In production, this should come from a secure environment variable
    this.secretKey =
      import.meta.env.VITE_ENCRYPTION_KEY || this.generateSecretKey();
  }

  private generateSecretKey(): string {
    // Generate a random key if none is provided (for development only)
    return CryptoJS.lib.WordArray.random(256 / 8).toString();
  }

  encrypt(text: string): string {
    try {
      const encrypted = CryptoJS.AES.encrypt(text, this.secretKey).toString();
      return encrypted;
    } catch (error) {
      console.error("Encryption error:", error);
      throw new Error("Failed to encrypt data");
    }
  }

  decrypt(encryptedText: string): string {
    try {
      const bytes = CryptoJS.AES.decrypt(encryptedText, this.secretKey);
      const decrypted = bytes.toString(CryptoJS.enc.Utf8);
      if (!decrypted) {
        throw new Error("Decryption failed - invalid data or key");
      }
      return decrypted;
    } catch (error) {
      console.error("Decryption error:", error);
      throw new Error("Failed to decrypt data");
    }
  }

  hash(text: string): string {
    try {
      return CryptoJS.SHA256(text).toString();
    } catch (error) {
      console.error("Hashing error:", error);
      throw new Error("Failed to hash data");
    }
  }

  generateSalt(): string {
    return CryptoJS.lib.WordArray.random(128 / 8).toString();
  }

  hashWithSalt(text: string, salt: string): string {
    try {
      return CryptoJS.SHA256(text + salt).toString();
    } catch (error) {
      console.error("Salted hashing error:", error);
      throw new Error("Failed to hash data with salt");
    }
  }

  validateApiKey(apiKey: string): boolean {
    // Basic validation for API key format
    if (!apiKey || typeof apiKey !== "string") {
      return false;
    }

    // Check for common API key patterns
    const patterns = {
      openai: /^sk-[a-zA-Z0-9]{48}$/,
      claude: /^sk-ant-[a-zA-Z0-9-_]{95}$/,
      gemini: /^[a-zA-Z0-9-_]{39}$/,
      mistral: /^[a-zA-Z0-9]{32}$/,
      groq: /^gsk_[a-zA-Z0-9]{52}$/,
    };

    return Object.values(patterns).some((pattern) => pattern.test(apiKey));
  }

  maskApiKey(apiKey: string): string {
    if (!apiKey || apiKey.length < 8) {
      return "••••••••";
    }
    const start = apiKey.substring(0, 4);
    const end = apiKey.substring(apiKey.length - 4);
    const middle = "•".repeat(Math.max(8, apiKey.length - 8));
    return `${start}${middle}${end}`;
  }
}

export default EncryptionService.getInstance();
