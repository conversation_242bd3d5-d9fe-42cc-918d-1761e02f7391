import { decodeJWT, isJWTValid, isJWTExpiringSoon } from "@/lib/jwt-utils";
import { User, AuthState } from "@/types";

const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL || "http://localhost:3001";

class AuthService {
  private static instance: AuthService;
  private authState: AuthState = {
    user: null,
    token: null,
    isAuthenticated: false,
    isLoading: false,
  };
  private listeners: ((state: AuthState) => void)[] = [];

  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  constructor() {
    this.initializeAuth();
  }

  private initializeAuth() {
    const token = localStorage.getItem("auth_token");
    const demoUser = localStorage.getItem("demo_user");

    // Check for demo user first
    if (demoUser && token?.startsWith("demo-token-")) {
      try {
        const user = JSON.parse(demoUser) as User;
        this.authState = {
          user,
          token,
          isAuthenticated: true,
          isLoading: false,
        };
        this.notifyListeners();
        return;
      } catch (error) {
        console.error("Failed to parse demo user:", error);
      }
    }

    // Regular token validation
    if (token && this.isTokenValid(token)) {
      this.setToken(token);
    } else {
      this.clearAuth();
    }
  }

  private isTokenValid(token: string): boolean {
    try {
      return isJWTValid(token);
    } catch (error) {
      console.error("Token validation error:", error);
      return false;
    }
  }

  private setToken(token: string) {
    try {
      const decoded = decodeJWT(token);
      if (!decoded) throw new Error("Failed to decode token");
      this.authState = {
        user: {
          id: decoded.sub || "",
          email: decoded.email || "",
          name: decoded.name || "",
          role: (decoded.role as "admin" | "user") || "user",
          createdAt: decoded.createdAt
            ? new Date(decoded.createdAt)
            : new Date(),
          lastActive: new Date(),
          permissions: []
        },
        token,
        isAuthenticated: true,
        isLoading: false,
      };
      localStorage.setItem("auth_token", token);
      this.notifyListeners();
    } catch (error) {
      console.error("Invalid token:", error);
      this.clearAuth();
    }
  }

  private clearAuth() {
    this.authState = {
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
    };
    localStorage.removeItem("auth_token");
    localStorage.removeItem("demo_user");
    this.notifyListeners();
  }

  setDemoUser(user: User) {
    const demoToken = `demo-token-${user.id}-${Date.now()}`;
    this.authState = {
      user,
      token: demoToken,
      isAuthenticated: true,
      isLoading: false,
    };
    localStorage.setItem("demo_user", JSON.stringify(user));
    localStorage.setItem("auth_token", demoToken);
    this.notifyListeners();
  }

  private notifyListeners() {
    this.listeners.forEach((listener) => listener(this.authState));
  }

  subscribe(listener: (state: AuthState) => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter((l) => l !== listener);
    };
  }

  getAuthState(): AuthState {
    return { ...this.authState };
  }

  getToken(): string | null {
    return this.authState.token;
  }

  getUser(): User | null {
    return this.authState.user;
  }

  hasRole(role: string): boolean {
    return this.authState.user?.role === role;
  }

  isAdmin(): boolean {
    return this.hasRole("admin");
  }

  requireAuth(): boolean {
    if (!this.isAuthenticated()) {
      throw new Error("Authentication required");
    }
    return true;
  }

  requireAdmin(): boolean {
    this.requireAuth();
    if (!this.isAdmin()) {
      throw new Error("Admin privileges required");
    }
    return true;
  }

  isAuthenticated(): boolean {
    return this.authState.isAuthenticated && this.authState.token !== null;
  }

  async login(
    email: string,
    password: string,
  ): Promise<{ success: boolean; error?: string }> {
    this.authState.isLoading = true;
    this.notifyListeners();

    try {
      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (response.ok && data.token) {
        this.setToken(data.token);
        return { success: true };
      } else {
        this.authState.isLoading = false;
        this.notifyListeners();
        return { success: false, error: data.message || "Login failed" };
      }
    } catch (error) {
      this.authState.isLoading = false;
      this.notifyListeners();
      return { success: false, error: "Network error" };
    }
  }

  async register(
    email: string,
    password: string,
    name: string,
  ): Promise<{ success: boolean; error?: string }> {
    this.authState.isLoading = true;
    this.notifyListeners();

    try {
      const response = await fetch(`${API_BASE_URL}/auth/register`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, password, name }),
      });

      const data = await response.json();

      if (response.ok && data.token) {
        this.setToken(data.token);
        return { success: true };
      } else {
        this.authState.isLoading = false;
        this.notifyListeners();
        return { success: false, error: data.message || "Registration failed" };
      }
    } catch (error) {
      this.authState.isLoading = false;
      this.notifyListeners();
      return { success: false, error: "Network error" };
    }
  }

  async logout(): Promise<void> {
    try {
      if (this.authState.token) {
        await fetch(`${API_BASE_URL}/auth/logout`, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${this.authState.token}`,
          },
        });
      }
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      this.clearAuth();
    }
  }

  async refreshToken(): Promise<boolean> {
    if (!this.authState.token) return false;

    try {
      const response = await fetch(`${API_BASE_URL}/auth/refresh`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${this.authState.token}`,
        },
      });

      const data = await response.json();

      if (response.ok && data.token) {
        this.setToken(data.token);
        return true;
      } else {
        this.clearAuth();
        return false;
      }
    } catch (error) {
      console.error("Token refresh error:", error);
      this.clearAuth();
      return false;
    }
  }

  async makeAuthenticatedRequest(
    url: string,
    options: RequestInit = {},
  ): Promise<Response> {
    const token = this.getToken();
    if (!token) {
      throw new Error("No authentication token available");
    }

    const headers = {
      ...options.headers,
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    };

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (response.status === 401) {
      const refreshed = await this.refreshToken();
      if (refreshed) {
        // Retry with new token
        const newToken = this.getToken();
        return fetch(url, {
          ...options,
          headers: {
            ...options.headers,
            Authorization: `Bearer ${newToken}`,
            "Content-Type": "application/json",
          },
        });
      } else {
        throw new Error("Authentication failed");
      }
    }

    return response;
  }
}

export default AuthService.getInstance();
