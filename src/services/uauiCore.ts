import {
  AIPXRequest,
  AIPXResponse,
  SessionData,
  EventBusEvent,
  StateUpdate,
  ProviderResponse,
} from "@/types";
import AuthService from "./auth";
import RoutingRulesService from "./routingRules";
import APIProviderService from "./apiProvider";
import { v4 as uuidv4 } from "uuid";

interface SessionManager {
  createSession(
    userId: string,
    sessionData: Partial<SessionData>,
  ): Promise<string>;
  getSession(sessionId: string): Promise<SessionData | null>;
  updateSession(
    sessionId: string,
    updates: Partial<SessionData>,
  ): Promise<void>;
  deleteSession(sessionId: string): Promise<void>;
}

interface EventBus {
  emit(event: string, data: any): void;
  on(event: string, callback: (data: any) => void): () => void;
  off(event: string, callback: (data: any) => void): void;
}

class UAUISessionManager implements SessionManager {
  private sessions: Map<string, SessionData> = new Map();
  private readonly API_BASE_URL =
    import.meta.env.VITE_API_BASE_URL || "http://localhost:3001";

  async createSession(
    userId: string,
    sessionData: Partial<SessionData>,
  ): Promise<string> {
    const sessionId = uuidv4();
    const now = new Date();
    const session: SessionData = {
      id: sessionId,
      userId,
      context: {},
      createdAt: now,
      updatedAt: now,
      expiresAt: new Date(now.getTime() + 24 * 60 * 60 * 1000), // 24 hours
      ...sessionData,
    };

    this.sessions.set(sessionId, session);

    // Persist to backend
    try {
      await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/sessions`,
        {
          method: "POST",
          body: JSON.stringify(session),
        },
      );
    } catch (error) {
      console.error("Failed to persist session:", error);
    }

    return sessionId;
  }

  async getSession(sessionId: string): Promise<SessionData | null> {
    // Check local cache first
    const cachedSession = this.sessions.get(sessionId);
    if (cachedSession && cachedSession.expiresAt > new Date()) {
      return cachedSession;
    }

    // Fetch from backend
    try {
      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/sessions/${sessionId}`,
      );

      if (response.ok) {
        const session = await response.json();
        this.sessions.set(sessionId, session);
        return session;
      }
    } catch (error) {
      console.error("Failed to fetch session:", error);
    }

    return null;
  }

  async updateSession(
    sessionId: string,
    updates: Partial<SessionData>,
  ): Promise<void> {
    const session = await this.getSession(sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    const updatedSession = {
      ...session,
      ...updates,
      updatedAt: new Date(),
    };

    this.sessions.set(sessionId, updatedSession);

    // Persist to backend
    try {
      await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/sessions/${sessionId}`,
        {
          method: "PUT",
          body: JSON.stringify(updates),
        },
      );
    } catch (error) {
      console.error("Failed to update session:", error);
    }
  }

  async deleteSession(sessionId: string): Promise<void> {
    this.sessions.delete(sessionId);

    try {
      await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/sessions/${sessionId}`,
        {
          method: "DELETE",
        },
      );
    } catch (error) {
      console.error("Failed to delete session:", error);
    }
  }

  cleanupExpiredSessions(): void {
    const now = new Date();
    for (const [sessionId, session] of this.sessions.entries()) {
      if (session.expiresAt <= now) {
        this.deleteSession(sessionId);
      }
    }
  }
}

class UAUIEventBus implements EventBus {
  private listeners: Map<string, ((data: any) => void)[]> = new Map();

  emit(event: string, data: any): void {
    const callbacks = this.listeners.get(event) || [];
    callbacks.forEach((callback) => {
      try {
        callback(data);
      } catch (error) {
        console.error(`Error in event handler for ${event}:`, error);
      }
    });
  }

  on(event: string, callback: (data: any) => void): () => void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);

    // Return unsubscribe function
    return () => {
      this.off(event, callback);
    };
  }

  off(event: string, callback: (data: any) => void): void {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }
}

class UAUICoreService {
  private static instance: UAUICoreService;
  private sessionManager: UAUISessionManager;
  private eventBus: UAUIEventBus;
  private readonly API_BASE_URL =
    import.meta.env.VITE_API_BASE_URL || "http://localhost:3001";
  sessions: any;

  static getInstance(): UAUICoreService {
    if (!UAUICoreService.instance) {
      UAUICoreService.instance = new UAUICoreService();
    }
    return UAUICoreService.instance;
  }

  constructor() {
    this.sessionManager = new UAUISessionManager();
    this.eventBus = new UAUIEventBus();

    // Cleanup expired sessions every hour
    setInterval(
      () => {
        this.sessionManager.cleanupExpiredSessions();
      },
      60 * 60 * 1000,
    );
  }

  async processRequest(request: AIPXRequest): Promise<AIPXResponse> {
    try {
      const { userId, sessionId, message, appType, metadata } = request;

      // Get or create session
      let session = await this.sessionManager.getSession(sessionId);
      if (!session) {
        const newSessionId = await this.sessionManager.createSession(userId, {
          conversationId: metadata?.conversationId,
          agentId: metadata?.agentId,
        });
        session = await this.sessionManager.getSession(newSessionId);
      }

      if (!session) {
        throw new Error("Failed to create or retrieve session");
      }

      // Emit processing start event
      this.eventBus.emit("processing_start", {
        sessionId,
        userId,
        message,
        timestamp: new Date(),
      });

      // Select appropriate provider based on routing rules
      const selectedProvider = await this.selectProvider(message, session);

      // Get provider configuration and API keys
      const providerConfig = await this.getProviderConfig(selectedProvider);
      if (!providerConfig) {
        throw new Error(`Provider ${selectedProvider} not configured`);
      }

      // Initialize provider if not already done
      await APIProviderService.initializeProvider(
        selectedProvider,
        providerConfig.apiKey,
      );

      // Build conversation context
      const conversationHistory = await this.buildConversationHistory(
        session.conversationId || metadata?.conversationId,
      );

      // Process with AI provider
      const response = await this.processWithProvider({
        provider: selectedProvider,
        messages: conversationHistory,
        currentMessage: message,
        session,
        metadata,
      });

      // Update session with response
      await this.sessionManager.updateSession(sessionId, {
        lastMessage: message,
        lastResponse: response.content,
        provider: selectedProvider,
        context: {
          ...session.context,
          lastProvider: selectedProvider,
          messageCount: (session.context.messageCount || 0) + 1,
        },
      });

      // Emit processing complete event
      this.eventBus.emit("processing_complete", {
        sessionId,
        userId,
        response: response.content,
        provider: selectedProvider,
        timestamp: new Date(),
      });

      return {
        final: response.content,
        stream: false,
        state_update: {
          provider: selectedProvider,
          messageCount: (session.context.messageCount || 0) + 1,
        },
      };
    } catch (error) {
      console.error("UAUI processing error:", error);

      this.eventBus.emit("processing_error", {
        sessionId: request.sessionId,
        userId: request.userId,
        error: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date(),
      });

      return {
        error: error instanceof Error ? error.message : "Processing failed",
        final:
          "I apologize, but I encountered an error processing your request. Please try again.",
      };
    }
  }

  private async selectProvider(
    message: string,
    session: SessionData,
  ): Promise<string> {
    try {
      // Use routing rules service to determine provider
      const selectedProvider = RoutingRulesService.evaluateMessage(message);

      // Consider session context for provider selection
      if (session.context.preferredProvider) {
        return session.context.preferredProvider;
      }

      return selectedProvider;
    } catch (error) {
      console.error("Provider selection error:", error);
      return "openai"; // Default fallback
    }
  }

  private async getProviderConfig(
    providerId: string,
  ): Promise<{ apiKey: string } | null> {
    try {
      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/providers/${providerId}/config`,
      );

      if (response.ok) {
        const config = await response.json();
        return config;
      }
    } catch (error) {
      console.error(`Failed to get config for provider ${providerId}:`, error);
    }

    return null;
  }

  private async buildConversationHistory(
    conversationId?: string,
  ): Promise<Array<{ role: string; content: string }>> {
    if (!conversationId) {
      return [];
    }

    try {
      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/conversations/${conversationId}/messages`,
      );

      if (response.ok) {
        const data = await response.json();
        return data.messages.map((msg: any) => ({
          role: msg.role,
          content: msg.content,
        }));
      }
    } catch (error) {
      console.error("Failed to build conversation history:", error);
    }

    return [];
  }

  private async processWithProvider({
    provider,
    messages,
    currentMessage,
    session,
    metadata,
  }: {
    provider: string;
    messages: Array<{ role: string; content: string }>;
    currentMessage: string;
    session: SessionData;
    metadata?: Record<string, any>;
  }): Promise<ProviderResponse> {
    // Add current message to conversation
    const fullMessages = [
      ...messages,
      { role: "user", content: currentMessage },
    ];

    // Get system prompt if agent is specified
    if (session.agentId) {
      const systemPrompt = await this.getAgentSystemPrompt(session.agentId);
      if (systemPrompt) {
        fullMessages.unshift({ role: "system", content: systemPrompt });
      }
    }

    // Process with the selected provider
    return await APIProviderService.sendMessage(provider, fullMessages, {
      stream: false,
      maxTokens: 2000,
      temperature: 0.7,
    });
  }

  private async getAgentSystemPrompt(agentId: string): Promise<string | null> {
    try {
      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/agents/${agentId}`,
      );

      if (response.ok) {
        const agent = await response.json();
        return agent.systemPrompt || null;
      }
    } catch (error) {
      console.error(`Failed to get system prompt for agent ${agentId}:`, error);
    }

    return null;
  }

  // Event management methods
  on(event: string, callback: (data: any) => void): () => void {
    return this.eventBus.on(event, callback);
  }

  emit(event: string, data: any): void {
    this.eventBus.emit(event, data);
  }

  // Session management methods
  async createSession(
    userId: string,
    sessionData?: Partial<SessionData>,
  ): Promise<string> {
    return this.sessionManager.createSession(userId, sessionData || {});
  }

  async getSession(sessionId: string): Promise<SessionData | null> {
    return this.sessionManager.getSession(sessionId);
  }

  async updateSession(
    sessionId: string,
    updates: Partial<SessionData>,
  ): Promise<void> {
    return this.sessionManager.updateSession(sessionId, updates);
  }

  async deleteSession(sessionId: string): Promise<void> {
    return this.sessionManager.deleteSession(sessionId);
  }

  // State synchronization methods
  async syncState(
    fromAppId: string,
    toAppId: string,
    state: Record<string, any>,
  ): Promise<void> {
    try {
      await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/state/sync`,
        {
          method: "POST",
          body: JSON.stringify({
            fromAppId,
            toAppId,
            state,
            timestamp: new Date(),
          }),
        },
      );

      this.eventBus.emit("state_sync", {
        fromAppId,
        toAppId,
        state,
        timestamp: new Date(),
      });
    } catch (error) {
      console.error("State sync error:", error);
      throw error;
    }
  }

  // Health check and diagnostics
  async healthCheck(): Promise<{
    status: "healthy" | "degraded" | "unhealthy";
    services: Record<string, boolean>;
    timestamp: Date;
  }> {
    const services: Record<string, boolean> = {};
    let overallHealthy = true;

    // Check authentication service
    try {
      services.auth = AuthService.isAuthenticated();
    } catch {
      services.auth = false;
      overallHealthy = false;
    }

    // Check routing rules service
    try {
      const rules = RoutingRulesService.getRules();
      services.routing = rules.length > 0;
    } catch {
      services.routing = false;
      overallHealthy = false;
    }

    // Check API providers
    try {
      const providers = APIProviderService.getAvailableProviders();
      services.providers = providers.length > 0;
    } catch {
      services.providers = false;
      overallHealthy = false;
    }

    // Check backend connectivity
    try {
      const response = await fetch(`${this.API_BASE_URL}/health`);
      services.backend = response.ok;
    } catch {
      services.backend = false;
      overallHealthy = false;
    }

    const status = overallHealthy
      ? "healthy"
      : Object.values(services).some((s) => s)
        ? "degraded"
        : "unhealthy";

    return {
      status,
      services,
      timestamp: new Date(),
    };
  }

  // Analytics and metrics
  async getMetrics(): Promise<{
    totalSessions: number;
    activeSessions: number;
    totalRequests: number;
    averageResponseTime: number;
    errorRate: number;
  }> {
    try {
      const response = await AuthService.makeAuthenticatedRequest(
        `${this.API_BASE_URL}/api/metrics/uaui`,
      );

      if (response.ok) {
        return await response.json();
      }
    } catch (error) {
      console.error("Failed to get metrics:", error);
    }

    return {
      totalSessions: this.sessions.size,
      activeSessions: Array.from(this.sessions.values()).filter(
        (s: SessionData) => s.expiresAt > new Date(),
      ).length,
      totalRequests: 0,
      averageResponseTime: 0,
      errorRate: 0,
    };
  }
}

export default UAUICoreService.getInstance();
