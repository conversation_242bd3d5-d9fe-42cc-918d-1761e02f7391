export interface User {
  id: string;
  email: string;
  name: string;
  role: "admin" | "user" | "agent_builder" | "knowledge_manager";
  permissions: string[];
  organizationId?: string;
  createdAt: Date;
  lastActive: Date;
}

export interface UserWithStatus extends User {
  status: "active" | "inactive";
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

export interface Organization {
  id: string;
  name: string;
  slug: string;
  settings: OrganizationSettings;
  createdAt: Date;
  updatedAt: Date;
}

export interface OrganizationSettings {
  allowedDomains: string[];
  maxUsers: number;
  features: string[];
  branding: {
    logo?: string;
    primaryColor?: string;
    secondaryColor?: string;
  };
}

export interface ApiKey {
  id: string;
  provider: "openai" | "claude" | "gemini" | "mistral" | "groq";
  name: string;
  encryptedKey: string;
  lastUsed: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface ApiKeyWithStatus extends ApiKey {
  status: "active" | "inactive";
}

export interface RoutingRule {
  id: string;
  name: string;
  priority: number;
  condition: string;
  provider: "openai" | "claude" | "gemini" | "mistral" | "groq";
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Message {
  id: string;
  conversationId: string;
  role: "user" | "assistant" | "system";
  content: string;
  timestamp: Date;
  status?: "complete" | "streaming" | "thinking" | "error";
  provider?: string;
  toolCalls?: ToolCall[];
}

export interface ToolCall {
  id: string;
  tool: string;
  input: Record<string, any>;
  output?: Record<string, any>;
  status: "pending" | "complete" | "error";
}

export interface Conversation {
  id: string;
  userId: string;
  title: string;
  createdAt: Date;
  updatedAt: Date;
  messages: Message[];
}

export interface APIPEvent {
  type:
    | "message"
    | "thinking"
    | "form_request"
    | "tool_call"
    | "error"
    | "connection";
  data: any;
  conversationId?: string;
  messageId?: string;
}

export interface FormField {
  id: string;
  type: "text" | "textarea" | "select" | "checkbox" | "radio";
  label: string;
  placeholder?: string;
  required?: boolean;
  options?: { value: string; label: string }[];
  description?: string;
  defaultValue?: string | boolean;
  validation?: {
    pattern?: string;
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
  };
}

export interface DynamicFormData {
  id: string;
  title: string;
  description?: string;
  fields: FormField[];
}

export interface SystemPrompt {
  id: string;
  name: string;
  content: string;
  context: "general" | "technical" | "creative" | "support" | "custom";
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

export interface Agent {
  id: string;
  name: string;
  description: string;
  avatar?: string;
  systemPrompt: string;
  model: string;
  temperature: number;
  maxTokens: number;
  tools: string[];
  knowledgeBases: string[];
  isPublic: boolean;
  isActive: boolean;
  createdBy: string;
  organizationId?: string;
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface Tool {
  id: string;
  name: string;
  description: string;
  type: "function" | "api" | "webhook" | "database" | "custom";
  schema: ToolSchema;
  configuration: ToolConfiguration;
  isActive: boolean;
  createdBy: string;
  organizationId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ToolSchema {
  parameters: Record<string, any>;
  required: string[];
  returns: Record<string, any>;
}

export interface ToolConfiguration {
  endpoint?: string;
  method?: string;
  headers?: Record<string, string>;
  authentication?: {
    type: "none" | "bearer" | "api_key" | "oauth";
    credentials?: Record<string, string>;
  };
  timeout?: number;
  retries?: number;
}

export interface KnowledgeBase {
  id: string;
  name: string;
  description: string;
  type: "documents" | "qa" | "structured" | "web";
  status: "active" | "indexing" | "error" | "inactive";
  documentsCount: number;
  lastIndexed?: Date;
  settings: KnowledgeBaseSettings;
  createdBy: string;
  organizationId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface KnowledgeBaseSettings {
  chunkSize: number;
  chunkOverlap: number;
  embeddingModel: string;
  searchThreshold: number;
  maxResults: number;
  allowedFileTypes: string[];
}

export interface Document {
  id: string;
  knowledgeBaseId: string;
  name: string;
  type: string;
  size: number;
  content?: string;
  metadata: Record<string, any>;
  status: "processing" | "indexed" | "error";
  uploadedBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Widget {
  id: string;
  name: string;
  type: "chat" | "form" | "search" | "custom";
  agentId: string;
  configuration: WidgetConfiguration;
  appearance: WidgetAppearance;
  isActive: boolean;
  domain?: string;
  createdBy: string;
  organizationId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface WidgetConfiguration {
  welcomeMessage?: string;
  placeholder?: string;
  maxMessages?: number;
  allowFileUpload?: boolean;
  collectUserInfo?: boolean;
  enableFeedback?: boolean;
  customFields?: FormField[];
}

export interface WidgetAppearance {
  theme: "light" | "dark" | "auto";
  primaryColor: string;
  secondaryColor: string;
  fontFamily?: string;
  borderRadius?: number;
  position?: "bottom-right" | "bottom-left" | "top-right" | "top-left";
  size?: "small" | "medium" | "large";
}

export interface UserPreferences {
  id: string;
  userId: string;
  defaultProvider: "openai" | "claude" | "gemini" | "mistral" | "groq";
  defaultAgent?: string;
  enableThinkingIndicators: boolean;
  enableCodeHighlighting: boolean;
  enableNotifications: boolean;
  enableHITL: boolean;
  theme: "light" | "dark" | "system";
  language: string;
  systemPromptId?: string;
  favoriteAgents: string[];
  recentKnowledgeBases: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface HITLRequest {
  id: string;
  conversationId: string;
  messageId: string;
  type: "approval" | "input" | "clarification" | "escalation";
  title: string;
  description: string;
  context: Record<string, any>;
  status: "pending" | "approved" | "rejected" | "completed";
  assignedTo?: string;
  response?: string;
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  resolvedAt?: Date;
}

export interface Analytics {
  id: string;
  type: "conversation" | "agent" | "tool" | "knowledge_base" | "widget";
  entityId: string;
  event: string;
  data: Record<string, any>;
  userId?: string;
  sessionId?: string;
  organizationId?: string;
  timestamp: Date;
}

export interface Deployment {
  id: string;
  name: string;
  type: "widget" | "api" | "webhook";
  agentId: string;
  configuration: DeploymentConfiguration;
  status: "active" | "inactive" | "error";
  url?: string;
  apiKey?: string;
  metrics: DeploymentMetrics;
  createdBy: string;
  organizationId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface DeploymentConfiguration {
  environment: "development" | "staging" | "production";
  rateLimiting?: {
    requests: number;
    window: number;
  };
  authentication?: {
    required: boolean;
    type?: "api_key" | "jwt" | "oauth";
  };
  cors?: {
    origins: string[];
    methods: string[];
  };
  webhook?: {
    url: string;
    events: string[];
    secret?: string;
  };
}

export interface DeploymentMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  lastUsed?: Date;
  dailyUsage: Record<string, number>;
}

export interface AIProvider {
  id: string;
  name: string;
  baseUrl: string;
  supportsStreaming: boolean;
  supportsToolCalls: boolean;
  maxTokens: number;
  costPerToken: number;
}

export interface ProviderResponse {
  content: string;
  isComplete: boolean;
  toolCalls?: ToolCall[];
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: number;
}

export interface ConnectionStatus {
  status: "connected" | "disconnected" | "connecting" | "reconnecting";
  lastConnected?: Date;
  reconnectAttempts: number;
}

export interface WebSocketEvent {
  type: string;
  payload: any;
}

export interface APIPEvent {
  type:
    | "message"
    | "thinking"
    | "form_request"
    | "tool_call"
    | "error"
    | "connection";
  data: any;
}

export interface ThemeConfig {
  mode: "light" | "dark" | "system";
  primaryColor: string;
  accentColor: string;
}

export interface NavigationItem {
  id: string;
  label: string;
  icon: string;
  path: string;
  badge?: string | number;
  children?: NavigationItem[];
  permissions?: string[];
}

export interface BreadcrumbItem {
  label: string;
  path?: string;
}

export interface LoadingState {
  isLoading: boolean;
  message?: string;
  progress?: number;
}

export interface ErrorState {
  hasError: boolean;
  message?: string;
  code?: string;
  details?: any;
}

export interface PaginationConfig {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

export interface FilterConfig {
  field: string;
  operator:
    | "eq"
    | "ne"
    | "gt"
    | "lt"
    | "gte"
    | "lte"
    | "contains"
    | "startsWith"
    | "endsWith";
  value: any;
}

export interface SortConfig {
  field: string;
  direction: "asc" | "desc";
}

export interface SearchConfig {
  query: string;
  fields: string[];
}

export interface TableColumn {
  key: string;
  label: string;
  sortable?: boolean;
  filterable?: boolean;
  width?: string;
  align?: "left" | "center" | "right";
  render?: (value: any, row: any) => React.ReactNode;
}

export interface FormFieldConfig {
  name: string;
  label: string;
  type:
    | "text"
    | "email"
    | "password"
    | "textarea"
    | "select"
    | "checkbox"
    | "radio"
    | "file"
    | "date"
    | "number";
  placeholder?: string;
  required?: boolean;
  validation?: any;
  options?: { value: string; label: string }[];
  description?: string;
  disabled?: boolean;
}

export interface NotificationConfig {
  id: string;
  type: "success" | "error" | "warning" | "info";
  title: string;
  message?: string;
  duration?: number;
  persistent?: boolean;
  actions?: { label: string; action: () => void }[];
}

export interface Session {
  id: string;
  userId: string;
  agentId?: string;
  title: string;
  status: "active" | "completed" | "archived";
  messageCount: number;
  tokensUsed: number;
  cost: number;
  provider: string;
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  lastActivity: Date;
}

export interface Provider {
  id: string;
  name: string;
  type: "openai" | "claude" | "gemini" | "mistral" | "groq" | "custom";
  baseUrl: string;
  apiVersion?: string;
  models: ProviderModel[];
  capabilities: ProviderCapability[];
  pricing: ProviderPricing;
  status: "active" | "inactive" | "maintenance";
  configuration: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProviderModel {
  id: string;
  name: string;
  displayName: string;
  contextLength: number;
  maxTokens: number;
  supportsStreaming: boolean;
  supportsToolCalls: boolean;
  supportsVision: boolean;
  inputCostPer1k: number;
  outputCostPer1k: number;
}

export interface ProviderCapability {
  name: string;
  supported: boolean;
  limitations?: string[];
}

export interface ProviderPricing {
  model: string;
  inputCostPer1k: number;
  outputCostPer1k: number;
  currency: string;
}

export interface SystemMetrics {
  totalUsers: number;
  activeUsers: number;
  totalSessions: number;
  activeSessions: number;
  totalMessages: number;
  totalTokens: number;
  totalCost: number;
  averageResponseTime: number;
  errorRate: number;
  uptime: number;
  lastUpdated: Date;
}

export interface UsageMetrics {
  period: "hour" | "day" | "week" | "month";
  data: {
    timestamp: Date;
    users: number;
    sessions: number;
    messages: number;
    tokens: number;
    cost: number;
    errors: number;
  }[];
}

export interface PerformanceMetrics {
  provider: string;
  model: string;
  averageResponseTime: number;
  successRate: number;
  errorRate: number;
  throughput: number;
  costEfficiency: number;
  userSatisfaction: number;
  period: Date;
}

// SynapseAI Core Interfaces
export interface AIPXRequest {
  userId: string;
  sessionId: string;
  message: string;
  appType: "widget" | "dashboard" | "crm";
  metadata?: Record<string, any>;
}

export interface AIPXResponse {
  stream?: boolean;
  chunks?: string[];
  final?: string;
  tool_call?: {
    toolId: string;
    params: Record<string, any>;
  };
  error?: string;
  state_update?: Record<string, any>;
}

export interface AIPXApp {
  id: string;
  type: string;
  config: Record<string, any>;
}

export interface UAUIEngine {
  processRequest(request: AIPXRequest): Promise<AIPXResponse>;
  registerApp(appId: string, appType: string): AIPXApp;
  syncState(
    fromAppId: string,
    toAppId: string,
    state: Record<string, any>,
  ): void;
  onEvent(eventType: string, callback: (payload: any) => void): void;
}

export interface SessionData {
  id: string;
  userId: string;
  agentId?: string;
  conversationId?: string;
  provider?: string;
  lastMessage?: string;
  lastResponse?: string;
  context: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  expiresAt: Date;
}

export interface EventBusEvent {
  type: string;
  data: any;
  timestamp: number;
  source?: string;
  target?: string;
}

export interface StateUpdate {
  key: string;
  value: any;
  timestamp: number;
  source: string;
}

export interface ProviderAdapter {
  id: string;
  name: string;
  type: "openai" | "claude" | "gemini" | "mistral" | "groq";
  config: ProviderConfig;
  isActive: boolean;
  lastUsed?: Date;
}

export interface ProviderConfig {
  apiKey: string;
  baseUrl?: string;
  model?: string;
  maxTokens?: number;
  temperature?: number;
  timeout?: number;
  retries?: number;
}

export interface SmartRoutingRule {
  id: string;
  name: string;
  priority: number;
  condition: RoutingCondition;
  provider: string;
  isActive: boolean;
  metadata?: Record<string, any>;
}

export interface RoutingCondition {
  type: "contains" | "regex" | "length" | "sentiment" | "language" | "default";
  value?: string | number;
  options?: string[];
  operator?: "and" | "or";
  children?: RoutingCondition[];
}

export interface AgentBuilder {
  id: string;
  name: string;
  description: string;
  avatar?: string;
  systemPrompt: string;
  model: string;
  temperature: number;
  maxTokens: number;
  tools: string[];
  knowledgeBases: string[];
  workflows: AgentWorkflow[];
  isPublic: boolean;
  isActive: boolean;
  createdBy: string;
  organizationId?: string;
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface AgentWorkflow {
  id: string;
  name: string;
  steps: WorkflowStep[];
  triggers: WorkflowTrigger[];
  isActive: boolean;
}

export interface WorkflowStep {
  id: string;
  type: "tool_call" | "condition" | "response" | "hitl" | "delay";
  config: Record<string, any>;
  nextSteps: string[];
  conditions?: WorkflowCondition[];
}

export interface WorkflowTrigger {
  type: "message" | "event" | "schedule" | "webhook";
  config: Record<string, any>;
}

export interface WorkflowCondition {
  field: string;
  operator: "equals" | "contains" | "greater_than" | "less_than";
  value: any;
}

export interface ToolBuilder {
  id: string;
  name: string;
  description: string;
  type: "function" | "api" | "webhook" | "database" | "custom";
  schema: ToolSchema;
  configuration: ToolConfiguration;
  testCases: ToolTestCase[];
  isActive: boolean;
  createdBy: string;
  organizationId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ToolTestCase {
  id: string;
  name: string;
  input: Record<string, any>;
  expectedOutput: Record<string, any>;
  description?: string;
}

export interface KnowledgeBaseBuilder {
  id: string;
  name: string;
  description: string;
  type: "documents" | "qa" | "structured" | "web";
  status: "active" | "indexing" | "error" | "inactive";
  documentsCount: number;
  lastIndexed?: Date;
  settings: KnowledgeBaseSettings;
  sources: KnowledgeSource[];
  createdBy: string;
  organizationId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface KnowledgeSource {
  id: string;
  type: "file" | "url" | "text" | "database";
  source: string;
  metadata: Record<string, any>;
  status: "pending" | "processing" | "indexed" | "error";
  createdAt: Date;
}

export interface WidgetBuilder {
  id: string;
  name: string;
  type: "chat" | "form" | "search" | "custom";
  agentId: string;
  configuration: WidgetConfiguration;
  appearance: WidgetAppearance;
  embedCode: string;
  previewUrl: string;
  isActive: boolean;
  domain?: string;
  createdBy: string;
  organizationId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface WidgetEmbedOptions {
  type: "script" | "iframe" | "react" | "vue" | "angular";
  customization: {
    theme: "light" | "dark" | "auto";
    position:
      | "bottom-right"
      | "bottom-left"
      | "top-right"
      | "top-left"
      | "center";
    size: "small" | "medium" | "large" | "fullscreen";
    colors: {
      primary: string;
      secondary: string;
      background: string;
      text: string;
    };
  };
}

export interface LivePreviewSandbox {
  id: string;
  type: "agent" | "tool" | "widget" | "workflow";
  entityId: string;
  config: Record<string, any>;
  isActive: boolean;
  createdBy: string;
  createdAt: Date;
}

export interface AnalyticsDashboard {
  overview: SystemMetrics;
  usage: UsageMetrics;
  performance: PerformanceMetrics[];
  costs: CostMetrics;
  satisfaction: SatisfactionMetrics;
  alerts: AlertMetric[];
}

export interface CostMetrics {
  totalCost: number;
  costByProvider: Record<string, number>;
  costByAgent: Record<string, number>;
  costByUser: Record<string, number>;
  projectedMonthlyCost: number;
  period: {
    start: Date;
    end: Date;
  };
}

export interface SatisfactionMetrics {
  averageRating: number;
  totalRatings: number;
  ratingDistribution: Record<number, number>;
  feedbackCount: number;
  commonIssues: string[];
}

export interface AlertMetric {
  id: string;
  type: "error_rate" | "cost_threshold" | "performance" | "usage";
  severity: "low" | "medium" | "high" | "critical";
  message: string;
  value: number;
  threshold: number;
  timestamp: Date;
}

export interface UserRoleManager {
  id: string;
  name: string;
  permissions: Permission[];
  users: string[];
  organizationId?: string;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string;
  actions: string[];
  conditions?: PermissionCondition[];
}

export interface PermissionCondition {
  field: string;
  operator: "equals" | "not_equals" | "in" | "not_in";
  value: any;
}

export interface OrganizationManager {
  id: string;
  name: string;
  slug: string;
  settings: OrganizationSettings;
  users: OrganizationUser[];
  subscription: Subscription;
  createdAt: Date;
  updatedAt: Date;
}

export interface OrganizationUser {
  userId: string;
  role: string;
  permissions: string[];
  joinedAt: Date;
  lastActive: Date;
}

export interface Subscription {
  id: string;
  plan: "free" | "pro" | "enterprise";
  status: "active" | "cancelled" | "expired";
  limits: SubscriptionLimits;
  billingCycle: "monthly" | "yearly";
  nextBillingDate: Date;
  createdAt: Date;
}

export interface SubscriptionLimits {
  maxUsers: number;
  maxAgents: number;
  maxTools: number;
  maxKnowledgeBases: number;
  maxWidgets: number;
  maxMonthlyRequests: number;
  maxStorageGB: number;
}

export interface SDKConfig {
  apiKey: string;
  baseUrl: string;
  organizationId?: string;
  timeout?: number;
  retries?: number;
  debug?: boolean;
}

export interface WebSocketClient {
  connect(): Promise<void>;
  disconnect(): void;
  send(event: string, data: any): Promise<void>;
  on(event: string, callback: (data: any) => void): () => void;
  isConnected(): boolean;
  getConnectionStatus(): ConnectionStatus;
}

export interface CLICommand {
  name: string;
  description: string;
  options: CLIOption[];
  handler: (args: any) => Promise<void>;
}

export interface CLIOption {
  name: string;
  alias?: string;
  description: string;
  type: "string" | "number" | "boolean";
  required?: boolean;
  default?: any;
}

export interface DeploymentManager {
  id: string;
  name: string;
  type: "widget" | "api" | "webhook";
  agentId: string;
  configuration: DeploymentConfiguration;
  status: "active" | "inactive" | "error";
  url?: string;
  apiKey?: string;
  metrics: DeploymentMetrics;
  logs: DeploymentLog[];
  createdBy: string;
  organizationId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface DeploymentLog {
  id: string;
  level: "info" | "warn" | "error";
  message: string;
  metadata?: Record<string, any>;
  timestamp: Date;
}

export interface ProductionConfig {
  server: {
    port: number;
    host: string;
    ssl: {
      enabled: boolean;
      cert?: string;
      key?: string;
    };
  };
  database: {
    type: "postgresql" | "mysql" | "sqlite";
    host: string;
    port: number;
    database: string;
    username: string;
    password: string;
    ssl?: boolean;
  };
  redis: {
    host: string;
    port: number;
    password?: string;
    db?: number;
  };
  security: {
    jwtSecret: string;
    encryptionKey: string;
    corsOrigins: string[];
    rateLimiting: {
      windowMs: number;
      max: number;
    };
  };
  monitoring: {
    enabled: boolean;
    logLevel: "debug" | "info" | "warn" | "error";
    metricsEndpoint?: string;
  };
}
