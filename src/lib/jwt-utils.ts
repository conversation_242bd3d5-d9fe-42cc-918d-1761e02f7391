// Browser-compatible JWT utilities
// This replaces the Node.js jsonwebtoken library for browser use

export interface JWTPayload {
  sub?: string;
  email?: string;
  name?: string;
  role?: string;
  exp?: number;
  iat?: number;
  createdAt?: string;
  [key: string]: any;
}

export interface JWTHeader {
  alg: string;
  typ: string;
  [key: string]: any;
}

/**
 * Decode a JWT token without verification (browser-safe)
 * This is equivalent to jwt.decode() from jsonwebtoken
 */
export function decodeJWT(token: string): JWTPayload | null {
  try {
    if (!token || typeof token !== 'string') {
      return null;
    }

    // Split the token into parts
    const parts = token.split('.');
    if (parts.length !== 3) {
      console.error('Invalid JWT format: token must have 3 parts');
      return null;
    }

    // Decode the payload (second part)
    const payload = parts[1];
    
    // Add padding if needed for base64 decoding
    const paddedPayload = payload + '='.repeat((4 - payload.length % 4) % 4);
    
    try {
      // Decode base64url to base64
      const base64 = paddedPayload.replace(/-/g, '+').replace(/_/g, '/');
      const decoded = atob(base64);
      return JSON.parse(decoded);
    } catch (error) {
      console.error('Failed to decode JWT payload:', error);
      return null;
    }
  } catch (error) {
    console.error('JWT decode error:', error);
    return null;
  }
}

/**
 * Decode JWT header
 */
export function decodeJWTHeader(token: string): JWTHeader | null {
  try {
    if (!token || typeof token !== 'string') {
      return null;
    }

    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    const header = parts[0];
    const paddedHeader = header + '='.repeat((4 - header.length % 4) % 4);
    
    try {
      const base64 = paddedHeader.replace(/-/g, '+').replace(/_/g, '/');
      const decoded = atob(base64);
      return JSON.parse(decoded);
    } catch (error) {
      console.error('Failed to decode JWT header:', error);
      return null;
    }
  } catch (error) {
    console.error('JWT header decode error:', error);
    return null;
  }
}

/**
 * Check if a JWT token is expired
 */
export function isJWTExpired(token: string): boolean {
  try {
    const payload = decodeJWT(token);
    if (!payload || !payload.exp) {
      return true; // Assume expired if we can't decode or no expiration
    }

    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp < currentTime;
  } catch (error) {
    console.error('Error checking JWT expiration:', error);
    return true; // Assume expired on error
  }
}

/**
 * Check if a JWT token is valid (not expired and properly formatted)
 */
export function isJWTValid(token: string): boolean {
  try {
    if (!token || typeof token !== 'string') {
      return false;
    }

    // Check format
    const parts = token.split('.');
    if (parts.length !== 3) {
      return false;
    }

    // Check if we can decode the payload
    const payload = decodeJWT(token);
    if (!payload) {
      return false;
    }

    // Check expiration
    return !isJWTExpired(token);
  } catch (error) {
    console.error('JWT validation error:', error);
    return false;
  }
}

/**
 * Get the time until JWT expiration in milliseconds
 */
export function getJWTTimeUntilExpiry(token: string): number {
  try {
    const payload = decodeJWT(token);
    if (!payload || !payload.exp) {
      return 0;
    }

    const expirationTime = payload.exp * 1000; // Convert to milliseconds
    const currentTime = Date.now();
    return Math.max(0, expirationTime - currentTime);
  } catch (error) {
    console.error('Error getting JWT time until expiry:', error);
    return 0;
  }
}

/**
 * Check if JWT is expiring soon (within specified minutes)
 */
export function isJWTExpiringSoon(token: string, minutesThreshold: number = 5): boolean {
  try {
    const timeUntilExpiry = getJWTTimeUntilExpiry(token);
    const thresholdMs = minutesThreshold * 60 * 1000;
    return timeUntilExpiry < thresholdMs;
  } catch (error) {
    console.error('Error checking if JWT is expiring soon:', error);
    return true; // Assume expiring soon on error
  }
}
