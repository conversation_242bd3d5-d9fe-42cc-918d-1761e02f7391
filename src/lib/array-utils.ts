// Utility functions for safe array operations

/**
 * Safe Array.from implementation that falls back to alternative methods
 * if Array.from is not available
 */
export function safeArrayFrom<T, U = T>(
  arrayLike: ArrayLike<T> | Iterable<T>,
  mapFn?: (v: T, k: number) => U,
  thisArg?: any
): U[] {
  try {
    // First, check if Array.from exists and is a function
    if (typeof Array !== 'undefined' && typeof Array.from === 'function') {
      return Array.from(arrayLike, mapFn, thisArg);
    }
    
    // Fallback implementation
    console.warn('Array.from not available, using fallback implementation');
    
    // Handle the case where arrayLike has a length property (like { length: 5 })
    if (arrayLike && typeof (arrayLike as any).length === 'number') {
      const length = (arrayLike as any).length;
      const result: U[] = [];
      
      for (let i = 0; i < length; i++) {
        const value = (arrayLike as any)[i];
        if (mapFn) {
          result[i] = thisArg ? mapFn.call(thisArg, value, i) : mapFn(value, i);
        } else {
          result[i] = value as unknown as U;
        }
      }
      
      return result;
    }
    
    // Handle iterable objects
    if (arrayLike && typeof (arrayLike as any)[Symbol.iterator] === 'function') {
      const result: U[] = [];
      let index = 0;
      
      for (const value of arrayLike as Iterable<T>) {
        if (mapFn) {
          result.push(thisArg ? mapFn.call(thisArg, value, index) : mapFn(value, index));
        } else {
          result.push(value as unknown as U);
        }
        index++;
      }
      
      return result;
    }
    
    // Last resort: convert to array using spread operator if possible
    if (Array.isArray(arrayLike)) {
      const result = [...arrayLike];
      if (mapFn) {
        return result.map((value, index) => 
          thisArg ? mapFn.call(thisArg, value, index) : mapFn(value, index)
        );
      }
      return result as unknown as U[];
    }
    
    throw new Error('Unable to convert arrayLike to array');
    
  } catch (error) {
    console.error('Error in safeArrayFrom:', error);
    console.error('arrayLike:', arrayLike);
    console.error('Array available:', typeof Array !== 'undefined');
    console.error('Array.from available:', typeof Array !== 'undefined' && typeof Array.from === 'function');
    
    // Return empty array as last resort
    return [];
  }
}

/**
 * Create an array of specified length filled with undefined values
 */
export function createArrayOfLength(length: number): undefined[] {
  try {
    return safeArrayFrom({ length });
  } catch (error) {
    console.error('Error creating array of length:', error);
    // Fallback to manual creation
    const result: undefined[] = [];
    for (let i = 0; i < length; i++) {
      result.push(undefined);
    }
    return result;
  }
}

/**
 * Create an array of numbers from 0 to length-1
 */
export function createNumberArray(length: number): number[] {
  try {
    return safeArrayFrom({ length }, (_, i) => i);
  } catch (error) {
    console.error('Error creating number array:', error);
    // Fallback to manual creation
    const result: number[] = [];
    for (let i = 0; i < length; i++) {
      result.push(i);
    }
    return result;
  }
}
