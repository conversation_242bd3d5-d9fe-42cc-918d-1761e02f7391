import { Suspense } from "react";
import { useRoutes, Routes, Route, useNavigate } from "react-router-dom";
import { ThemeProvider } from "./components/ThemeProvider";
import Home from "./components/home";
import WebSocketInitializer from "./components/WebSocketInitializer";
import routes from "tempo-routes";
import LandingPage from "./components/LandingPage";

function App() {
  const navigate = useNavigate();

  const handleGetStarted = () => {
    // Navigate to the main app or sign-up page
    // For now, we'll navigate to the home page
    navigate("/home");
  };

  const handleLogin = () => {
    // Navigate to login page or show login modal
    // For now, we'll navigate to the home page
    navigate("/home");
  };

  const handleSignUp = () => {
    // Navigate to sign-up page
    // For now, we'll navigate to the home page
    navigate("/home");
  };

  return (
    <ThemeProvider defaultTheme="system" storageKey="synapse-ui-theme">
      <Suspense
        fallback={
          <div className="flex items-center justify-center min-h-screen bg-background">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading SynapseAI...</p>
            </div>
          </div>
        }
      >
        <>
          <WebSocketInitializer />
          <Routes>
            <Route
              path="/"
              element={
                <LandingPage
                  onGetStarted={handleGetStarted}
                  onLogin={handleLogin}
                  onSignUp={handleSignUp}
                />
              }
            />
            <Route path="/home" element={<Home />} />
          </Routes>
          {import.meta.env.VITE_TEMPO === "true" && useRoutes(routes)}
        </>
      </Suspense>
    </ThemeProvider>
  );
}

export default App;
