# Admin Panel Components

This directory contains the refactored admin panel components, broken down into smaller, more manageable pieces.

## Structure

```
admin/
├── AdminPanel.tsx          # Original large component (legacy)
├── AdminPanelNew.tsx       # New refactored main component
├── OverviewTab.tsx         # Legacy overview tab (to be moved)
├── README.md              # This file
└── tabs/                  # Individual tab components
    ├── index.ts           # Export all tab components
    ├── OverviewTab.tsx    # System overview and metrics
    ├── AgentsTab.tsx      # AI agents management
    ├── ToolsTab.tsx       # Custom tools management
    ├── UsersTab.tsx       # User management
    ├── AnalyticsTab.tsx   # Analytics and reporting
    ├── KnowledgeTab.tsx   # Knowledge bases management
    └── ApiKeysTab.tsx     # API keys management
```

## Components

### AdminPanelNew.tsx
The main admin panel component that orchestrates all tabs. Features:
- Tab navigation with icons
- Centralized state management
- Mock data for demonstration
- Error handling and loading states
- Responsive design

### Tab Components

Each tab component is self-contained and follows consistent patterns:

#### OverviewTab
- System status dashboard
- Key performance metrics
- Real-time health indicators
- Recent activity summary

#### AgentsTab
- AI agents listing and management
- Create/edit/delete agents
- Status toggle functionality
- Search and filtering

#### ToolsTab
- Custom tools management
- Tool type categorization
- Testing capabilities
- Configuration management

#### UsersTab
- User account management
- Role-based permissions
- Activity tracking
- User creation/editing

#### AnalyticsTab
- Usage analytics and trends
- Provider usage breakdown
- Cost analysis
- Performance metrics

#### KnowledgeTab
- Knowledge base management
- Document indexing status
- Upload and processing
- Search capabilities

#### ApiKeysTab
- AI provider API key management
- Secure key storage and display
- Key testing functionality
- Provider-specific configuration

## Usage

### Using the New Admin Panel

```tsx
import AdminPanel from '@/components/admin/AdminPanelNew';

function App() {
  return <AdminPanel />;
}
```

### Using Individual Tab Components

```tsx
import { AgentsTab, ToolsTab } from '@/components/admin/tabs';

function CustomAdminView() {
  return (
    <div>
      <AgentsTab 
        agents={agents}
        onRefresh={handleRefresh}
        // ... other props
      />
      <ToolsTab 
        tools={tools}
        onRefresh={handleRefresh}
        // ... other props
      />
    </div>
  );
}
```

## Features

### Consistent Interface
All tab components follow the same interface pattern:
- Search functionality
- Status filtering
- Refresh capability
- CRUD operations
- Responsive design

### State Management
- Centralized state in main component
- Props drilling for data and handlers
- Consistent error handling
- Loading states

### UI/UX
- Consistent styling with shadcn/ui
- Responsive design
- Accessible components
- Loading and error states
- Toast notifications

## Migration Notes

To migrate from the old AdminPanel.tsx to the new structure:

1. Replace imports:
   ```tsx
   // Old
   import AdminPanel from '@/components/admin/AdminPanel';
   
   // New
   import AdminPanel from '@/components/admin/AdminPanelNew';
   ```

2. The API remains the same - no props changes required

3. Individual tab components can be used independently if needed

## Development

### Adding New Tabs

1. Create new tab component in `tabs/` directory
2. Follow existing patterns for props and structure
3. Add export to `tabs/index.ts`
4. Import and add to main AdminPanelNew component
5. Add tab trigger to TabsList

### Styling Guidelines

- Use shadcn/ui components consistently
- Follow existing spacing and layout patterns
- Maintain responsive design principles
- Use consistent icon sizing (h-4 w-4 for small, h-5 w-5 for medium)

### Testing

Each component should be tested for:
- Rendering with mock data
- Search and filter functionality
- CRUD operations
- Error states
- Loading states

## Future Improvements

- [ ] Add real API integration
- [ ] Implement proper error boundaries
- [ ] Add unit tests for each component
- [ ] Implement data caching
- [ ] Add keyboard navigation
- [ ] Improve accessibility
- [ ] Add drag-and-drop functionality where appropriate
- [ ] Implement real-time updates via WebSocket
