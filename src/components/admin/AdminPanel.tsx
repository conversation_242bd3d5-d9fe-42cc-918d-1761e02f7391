import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import {
  PlusCircle,
  Trash2,
  Edit,
  BarChart3,
  Key,
  Settings,
  Users,
  AlertTriangle,
  RefreshCw,
  TestTube,
  Eye,
  EyeOff,
  CheckCircle,
  XCircle,
  Search,
  Filter,
  Download,
  Upload,
  Database,
  Bot,
  Zap,
  Building,
  Activity,
  TrendingUp,
  Clock,
  DollarSign,
  Globe,
  Shield,
  Loader2,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  Copy,
  ExternalLink,
  FileText,
  MessageSquare,
  Server,
  Archive,
  UserPlus,
} from "lucide-react";
import AuthService from "@/services/auth";
import RoutingRulesService from "@/services/routingRules";
import ApiKeyManagementService from "@/services/apiKeyManagement";
import UserManagementService from "@/services/userManagement";
import SystemPromptsService from "@/services/systemPrompts";
import {
  ApiKey as ApiKeyType,
  RoutingRule as RoutingRuleType,
  SystemPrompt,
  ApiKeyWithStatus,
  Agent,
  Tool,
  KnowledgeBase,
  Widget,
  HITLRequest,
  Analytics,
  Deployment,
  Session,
  Organization,
  Provider,
  SystemMetrics,
  UsageMetrics,
  PerformanceMetrics,
  User,
} from "@/types";
import WebSocketStatusIndicator from "@/components/WebSocketStatusIndicator";
import OverviewTab from "./tabs/OverviewTab";

interface ApiKey {
  id: string;
  provider: string;
  name: string;
  key: string;
  status: "active" | "inactive";
  lastUsed: string;
}

interface RoutingRule {
  id: string;
  name: string;
  priority: number;
  condition: string;
  provider: string;
  isActive: boolean;
}

interface PerformanceMetric {
  provider: string;
  responseTime: number;
  successRate: number;
  costPerQuery: number;
  totalQueries: number;
}

/* Removed local User interface; using User type from "@/types" */

const AdminPanel = () => {
  const { toast } = useToast();

  // Real data state
  const [apiKeys, setApiKeys] = useState<ApiKeyWithStatus[]>([]);
  const [routingRules, setRoutingRules] = useState<RoutingRuleType[]>([]);
  const [performanceMetrics, setPerformanceMetrics] = useState<
    PerformanceMetric[]
  >([]);
  const [systemPrompts, setSystemPrompts] = useState<SystemPrompt[]>([]);
  const [agents, setAgents] = useState<Agent[]>([]);
  const [tools, setTools] = useState<Tool[]>([]);
  const [knowledgeBases, setKnowledgeBases] = useState<KnowledgeBase[]>([]);
  const [widgets, setWidgets] = useState<Widget[]>([]);
  const [hitlRequests, setHitlRequests] = useState<HITLRequest[]>([]);
  const [users, setUsers] = useState<import("@/types").User[]>([]);
  const [sessions, setSessions] = useState<Session[]>([]);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [providers, setProviders] = useState<Provider[]>([]);
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics | null>(
    null,
  );
  const [usageMetrics, setUsageMetrics] = useState<UsageMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Search and filter states
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("name");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // State for dialogs and forms
  const [apiKeyDialogOpen, setApiKeyDialogOpen] = useState(false);
  const [routingRuleDialogOpen, setRoutingRuleDialogOpen] = useState(false);
  const [userDialogOpen, setUserDialogOpen] = useState(false);
  const [systemPromptDialogOpen, setSystemPromptDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  const [selectedItemType, setSelectedItemType] = useState<
    "apiKey" | "routingRule" | "user" | "systemPrompt" | null
  >(null);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [showApiKey, setShowApiKey] = useState<Record<string, boolean>>({});
  const [testingApiKey, setTestingApiKey] = useState<string | null>(null);
  const [testingSystemPrompt, setTestingSystemPrompt] = useState<string | null>(
    null,
  );
  const [testResults, setTestResults] = useState<Record<string, any>>({});

  // Form states
  const [apiKeyForm, setApiKeyForm] = useState({
    provider: "",
    name: "",
    key: "",
  });
  const [routingRuleForm, setRoutingRuleForm] = useState({
    name: "",
    priority: 1,
    condition: "",
    provider: "",
  });
  const [userForm, setUserForm] = useState({
    name: "",
    email: "",
    password: "",
    role: "user" as "admin" | "user",
  });
  const [systemPromptForm, setSystemPromptForm] = useState({
    name: "",
    content: "",
    context: "general" as
      | "general"
      | "technical"
      | "creative"
      | "support"
      | "custom",
  });

  // Load data on component mount
  useEffect(() => {
    loadAllData();
  }, []);

  // Auto-refresh data every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      if (!loading && !refreshing) {
        refreshData();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [loading, refreshing]);

  const refreshData = async () => {
    setRefreshing(true);
    try {
      await loadAllData();
    } finally {
      setRefreshing(false);
    }
  };

  const loadAllData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Check if user is admin
      if (!AuthService.isAdmin()) {
        setError("Admin privileges required to access this panel");
        return;
      }

      const [
        apiKeysData,
        routingRulesData,
        usersData,
        systemPromptsData,
        agentsData,
        toolsData,
        knowledgeBasesData,
        widgetsData,
        hitlRequestsData,
        deploymentsData,
        sessionsData,
        organizationsData,
        providersData,
        systemMetricsData,
        usageMetricsData,
      ] = await Promise.all([
        ApiKeyManagementService.getApiKeys().catch((err) => {
          console.error("Failed to load API keys:", err);
          return [];
        }),
        RoutingRulesService.getRules(),
        UserManagementService.getUsers().catch((err) => {
          console.error("Failed to load users:", err);
          return [];
        }),
        SystemPromptsService.getSystemPrompts().catch((err) => {
          console.error("Failed to load system prompts:", err);
          return [];
        }),
        fetch("/api/agents")
          .then((res) => (res.ok ? res.json() : { agents: [] }))
          .catch(() => ({ agents: [] })),
        fetch("/api/tools")
          .then((res) => (res.ok ? res.json() : { tools: [] }))
          .catch(() => ({ tools: [] })),
        fetch("/api/knowledge-bases")
          .then((res) => (res.ok ? res.json() : { knowledgeBases: [] }))
          .catch(() => ({ knowledgeBases: [] })),
        fetch("/api/widgets")
          .then((res) => (res.ok ? res.json() : { widgets: [] }))
          .catch(() => ({ widgets: [] })),
        fetch("/api/hitl-requests")
          .then((res) => (res.ok ? res.json() : { requests: [] }))
          .catch(() => ({ requests: [] })),
        fetch("/api/deployments")
          .then((res) => (res.ok ? res.json() : { deployments: [] }))
          .catch(() => ({ deployments: [] })),
        fetch("/api/sessions")
          .then((res) => (res.ok ? res.json() : { sessions: [] }))
          .catch(() => ({ sessions: [] })),
        fetch("/api/organizations")
          .then((res) => (res.ok ? res.json() : { organizations: [] }))
          .catch(() => ({ organizations: [] })),
        fetch("/api/providers")
          .then((res) => (res.ok ? res.json() : { providers: [] }))
          .catch(() => ({ providers: [] })),
        fetch("/api/metrics/system")
          .then((res) => (res.ok ? res.json() : null))
          .catch(() => null),
        fetch("/api/metrics/usage")
          .then((res) => (res.ok ? res.json() : null))
          .catch(() => null),
      ]);

      setApiKeys(apiKeysData as ApiKeyWithStatus[]);
      setRoutingRules(routingRulesData);
      setUsers(usersData as import("@/types").User[]);
      setSystemPrompts(systemPromptsData);
      setAgents(agentsData.agents || []);
      setTools(toolsData.tools || []);
      setKnowledgeBases(knowledgeBasesData.knowledgeBases || []);
      setWidgets(widgetsData.widgets || []);
      setHitlRequests(hitlRequestsData.requests || []);
      setDeployments(deploymentsData.deployments || []);
      setSessions(sessionsData.sessions || []);
      setOrganizations(organizationsData.organizations || []);
      setProviders(providersData.providers || []);
      setSystemMetrics(systemMetricsData);
      setUsageMetrics(usageMetricsData);

      // Load performance metrics from API
      try {
        const metricsResponse = await fetch(
          `${import.meta.env.VITE_API_BASE_URL || "http://localhost:3001"}/api/metrics/performance`,
        );
        if (metricsResponse.ok) {
          const metricsData = await metricsResponse.json();
          setPerformanceMetrics(metricsData.metrics || []);
        } else {
          // Fallback to demo data for development
          setPerformanceMetrics([
            {
              provider: "OpenAI",
              responseTime: 1.2,
              successRate: 99.5,
              costPerQuery: 0.02,
              totalQueries: 15420,
            },
            {
              provider: "Claude",
              responseTime: 1.8,
              successRate: 98.7,
              costPerQuery: 0.015,
              totalQueries: 8750,
            },
            {
              provider: "Gemini",
              responseTime: 1.5,
              successRate: 97.8,
              costPerQuery: 0.01,
              totalQueries: 6230,
            },
            {
              provider: "Mistral",
              responseTime: 1.3,
              successRate: 98.2,
              costPerQuery: 0.008,
              totalQueries: 4120,
            },
            {
              provider: "Groq",
              responseTime: 0.9,
              successRate: 96.5,
              costPerQuery: 0.005,
              totalQueries: 2340,
            },
          ]);
        }
      } catch (error) {
        console.error("Failed to load performance metrics:", error);
        // Use demo data as fallback
        setPerformanceMetrics([
          {
            provider: "OpenAI",
            responseTime: 1.2,
            successRate: 99.5,
            costPerQuery: 0.02,
            totalQueries: 15420,
          },
          {
            provider: "Claude",
            responseTime: 1.8,
            successRate: 98.7,
            costPerQuery: 0.015,
            totalQueries: 8750,
          },
          {
            provider: "Gemini",
            responseTime: 1.5,
            successRate: 97.8,
            costPerQuery: 0.01,
            totalQueries: 6230,
          },
          {
            provider: "Mistral",
            responseTime: 1.3,
            successRate: 98.2,
            costPerQuery: 0.008,
            totalQueries: 4120,
          },
          {
            provider: "Groq",
            responseTime: 0.9,
            successRate: 96.5,
            costPerQuery: 0.005,
            totalQueries: 2340,
          },
        ]);
      }
    } catch (error) {
      console.error("Error loading admin data:", error);
      setError("Failed to load admin panel data");
    } finally {
      setLoading(false);
    }
  };

  // API Key handlers
  const handleAddApiKey = async () => {
    try {
      if (!apiKeyForm.provider || !apiKeyForm.name || !apiKeyForm.key) {
        setError("All fields are required");
        return;
      }

      const newApiKey = await ApiKeyManagementService.createApiKey({
        provider: apiKeyForm.provider as any,
        name: apiKeyForm.name,
        key: apiKeyForm.key,
      });

      setApiKeys((prev) => [...prev, newApiKey as ApiKeyWithStatus]);
      setApiKeyDialogOpen(false);
      setApiKeyForm({ provider: "", name: "", key: "" });
      setError(null);

      toast({
        title: "Success",
        description: "API key created successfully",
      });
    } catch (error: any) {
      setError(error.message || "Failed to create API key");
      toast({
        title: "Error",
        description: error.message || "Failed to create API key",
        variant: "destructive",
      });
    }
  };

  const handleEditApiKey = async () => {
    try {
      if (!editingItem || !apiKeyForm.name) {
        setError("Name is required");
        return;
      }

      const updates: any = { name: apiKeyForm.name };
      if (apiKeyForm.key) {
        updates.key = apiKeyForm.key;
      }

      const updatedApiKey = await ApiKeyManagementService.updateApiKey(
        editingItem.id,
        updates,
      );

      setApiKeys((prev) =>
        prev.map((key) =>
          key.id === editingItem.id ? (updatedApiKey as ApiKeyWithStatus) : key,
        ),
      );
      setApiKeyDialogOpen(false);
      setEditingItem(null);
      setApiKeyForm({ provider: "", name: "", key: "" });
      setError(null);

      toast({
        title: "Success",
        description: "API key updated successfully",
      });
    } catch (error: any) {
      setError(error.message || "Failed to update API key");
      toast({
        title: "Error",
        description: error.message || "Failed to update API key",
        variant: "destructive",
      });
    }
  };

  const handleTestApiKey = async (keyId: string) => {
    try {
      setTestingApiKey(keyId);
      const result = await ApiKeyManagementService.testApiKey(keyId);
      setTestResults((prev) => ({ ...prev, [keyId]: result }));
    } catch (error: any) {
      setTestResults((prev) => ({
        ...prev,
        [keyId]: { success: false, message: error.message },
      }));
    } finally {
      setTestingApiKey(null);
    }
  };

  // Routing Rule handlers
  const handleAddRoutingRule = async () => {
    try {
      if (
        !routingRuleForm.name ||
        !routingRuleForm.condition ||
        !routingRuleForm.provider
      ) {
        setError("All fields are required");
        return;
      }

      const newRule = await RoutingRulesService.createRule({
        name: routingRuleForm.name,
        priority: routingRuleForm.priority,
        condition: routingRuleForm.condition,
        provider: routingRuleForm.provider as any,
        isActive: true,
      });

      setRoutingRules((prev) => [...prev, newRule]);
      setRoutingRuleDialogOpen(false);
      setRoutingRuleForm({
        name: "",
        priority: 1,
        condition: "",
        provider: "",
      });
      setError(null);
    } catch (error: any) {
      setError(error.message || "Failed to create routing rule");
    }
  };

  const handleEditRoutingRule = async () => {
    try {
      if (
        !editingItem ||
        !routingRuleForm.name ||
        !routingRuleForm.condition ||
        !routingRuleForm.provider
      ) {
        setError("All fields are required");
        return;
      }

      const updatedRule = await RoutingRulesService.updateRule(editingItem.id, {
        name: routingRuleForm.name,
        priority: routingRuleForm.priority,
        condition: routingRuleForm.condition,
        provider: routingRuleForm.provider as any,
      });

      setRoutingRules((prev) =>
        prev.map((rule) => (rule.id === editingItem.id ? updatedRule : rule)),
      );
      setRoutingRuleDialogOpen(false);
      setEditingItem(null);
      setRoutingRuleForm({
        name: "",
        priority: 1,
        condition: "",
        provider: "",
      });
      setError(null);
    } catch (error: any) {
      setError(error.message || "Failed to update routing rule");
    }
  };

  const handleTestRoutingRule = async (ruleId: string) => {
    const testMessage = prompt(
      "Enter a test message to evaluate against this rule:",
    );
    if (!testMessage) return;

    try {
      const result = await RoutingRulesService.testRule(ruleId, testMessage);
      alert(
        `Test Result:\nMatches: ${result.matches}\nProvider: ${result.provider}`,
      );
    } catch (error: any) {
      alert(`Test failed: ${error.message}`);
    }
  };

  // User handlers
  const handleAddUser = async () => {
    try {
      if (!userForm.name || !userForm.email || !userForm.password) {
        setError("All fields are required");
        return;
      }

      const newUser = await UserManagementService.createUser({
        name: userForm.name,
        email: userForm.email,
        password: userForm.password,
        role: userForm.role,
      });

      setUsers((prev) => [...prev, newUser as unknown as User]);
      setUserDialogOpen(false);
      setUserForm({ name: "", email: "", password: "", role: "user" });
      setError(null);
    } catch (error: any) {
      setError(error.message || "Failed to create user");
    }
  };

  const handleEditUser = async () => {
    try {
      if (!editingItem || !userForm.name || !userForm.email) {
        setError("Name and email are required");
        return;
      }

      const updates: any = {
        name: userForm.name,
        email: userForm.email,
        role: userForm.role,
      };

      const updatedUser = await UserManagementService.updateUser(
        editingItem.id,
        updates,
      );

      setUsers((prev) =>
        prev.map((user) =>
          user.id === editingItem.id ? (updatedUser as unknown as User) : user,
        ),
      );
      setUserDialogOpen(false);
      setEditingItem(null);
      setUserForm({ name: "", email: "", password: "", role: "user" });
      setError(null);
    } catch (error: any) {
      setError(error.message || "Failed to update user");
    }
  };

  // System Prompt handlers
  const handleAddSystemPrompt = async () => {
    try {
      if (
        !systemPromptForm.name ||
        !systemPromptForm.content ||
        !systemPromptForm.context
      ) {
        setError("All fields are required");
        return;
      }

      const newPrompt = await SystemPromptsService.createSystemPrompt({
        name: systemPromptForm.name,
        content: systemPromptForm.content,
        context: systemPromptForm.context,
      });

      setSystemPrompts((prev) => [...prev, newPrompt]);
      setSystemPromptDialogOpen(false);
      setSystemPromptForm({
        name: "",
        content: "",
        context: "general",
      });
      setError(null);
    } catch (error: any) {
      setError(error.message || "Failed to create system prompt");
    }
  };

  const handleEditSystemPrompt = async () => {
    try {
      if (
        !editingItem ||
        !systemPromptForm.name ||
        !systemPromptForm.content ||
        !systemPromptForm.context
      ) {
        setError("All fields are required");
        return;
      }

      const updatedPrompt = await SystemPromptsService.updateSystemPrompt(
        editingItem.id,
        {
          name: systemPromptForm.name,
          content: systemPromptForm.content,
          context: systemPromptForm.context,
        },
      );

      setSystemPrompts((prev) =>
        prev.map((prompt) =>
          prompt.id === editingItem.id ? updatedPrompt : prompt,
        ),
      );
      setSystemPromptDialogOpen(false);
      setEditingItem(null);
      setSystemPromptForm({
        name: "",
        content: "",
        context: "general",
      });
      setError(null);
    } catch (error: any) {
      setError(error.message || "Failed to update system prompt");
    }
  };

  const handleTestSystemPrompt = async (promptId: string) => {
    const testMessage = prompt(
      "Enter a test message to evaluate with this system prompt:",
    );
    if (!testMessage) return;

    try {
      setTestingSystemPrompt(promptId);
      const result = await SystemPromptsService.testSystemPrompt(
        promptId,
        testMessage,
      );
      setTestResults((prev) => ({ ...prev, [promptId]: result }));

      if (result.success) {
        alert(`Test Result:\n\nResponse: ${result.response}`);
      } else {
        alert(`Test failed: ${result.error}`);
      }
    } catch (error: any) {
      alert(`Test failed: ${error.message}`);
    } finally {
      setTestingSystemPrompt(null);
    }
  };

  // Delete handler
  const handleDeleteItem = async () => {
    try {
      if (!selectedItemId || !selectedItemType) return;

      switch (selectedItemType) {
        case "apiKey":
          await ApiKeyManagementService.deleteApiKey(selectedItemId);
          setApiKeys((prev) => prev.filter((key) => key.id !== selectedItemId));
          break;
        case "routingRule":
          await RoutingRulesService.deleteRule(selectedItemId);
          setRoutingRules((prev) =>
            prev.filter((rule) => rule.id !== selectedItemId),
          );
          break;
        case "user":
          await UserManagementService.deleteUser(selectedItemId);
          setUsers((prev) => prev.filter((user) => user.id !== selectedItemId));
          break;
        case "systemPrompt":
          await SystemPromptsService.deleteSystemPrompt(selectedItemId);
          setSystemPrompts((prev) =>
            prev.filter((prompt) => prompt.id !== selectedItemId),
          );
          break;
      }

      setDeleteDialogOpen(false);
      setSelectedItemId(null);
      setSelectedItemType(null);
      setError(null);
    } catch (error: any) {
      setError(error.message || "Failed to delete item");
    }
  };

  // Toggle status handler
  const handleToggleStatus = async (
    id: string,
    type: "apiKey" | "routingRule" | "user" | "systemPrompt",
  ) => {
    try {
      switch (type) {
        case "apiKey":
          const updatedApiKey =
            await ApiKeyManagementService.toggleApiKeyStatus(id);
          setApiKeys((prev) =>
            prev.map((key) =>
              key.id === id ? (updatedApiKey as ApiKeyWithStatus) : key,
            ),
          );
          break;
        case "routingRule":
          const rule = routingRules.find((r) => r.id === id);
          if (rule) {
            const updatedRule = await RoutingRulesService.updateRule(id, {
              isActive: !rule.isActive,
            });
            setRoutingRules((prev) =>
              prev.map((r) => (r.id === id ? updatedRule : r)),
            );
          }
          break;
        case "user":
          const updatedUser = await UserManagementService.toggleUserStatus(id);
          setUsers((prev) =>
            prev.map((user) =>
              user.id === id ? (updatedUser as unknown as User) : user,
            ),
          );
          break;
        case "systemPrompt":
          const updatedPrompt =
            await SystemPromptsService.toggleSystemPromptStatus(id);
          setSystemPrompts((prev) =>
            prev.map((prompt) => (prompt.id === id ? updatedPrompt : prompt)),
          );
          break;
      }
      setError(null);
    } catch (error: any) {
      setError(error.message || "Failed to toggle status");
    }
  };

  // Helper functions
  const openEditDialog = (
    item: any,
    type: "apiKey" | "routingRule" | "user" | "systemPrompt",
  ) => {
    setEditingItem(item);

    switch (type) {
      case "apiKey":
        setApiKeyForm({ provider: item.provider, name: item.name, key: "" });
        setApiKeyDialogOpen(true);
        break;
      case "routingRule":
        setRoutingRuleForm({
          name: item.name,
          priority: item.priority,
          condition: item.condition,
          provider: item.provider,
        });
        setRoutingRuleDialogOpen(true);
        break;
      case "user":
        setUserForm({
          name: item.name,
          email: item.email,
          password: "",
          role: item.role,
        });
        setUserDialogOpen(true);
        break;
      case "systemPrompt":
        setSystemPromptForm({
          name: item.name,
          content: item.content,
          context: item.context,
        });
        setSystemPromptDialogOpen(true);
        break;
    }
  };

  const openDeleteDialog = (
    id: string,
    type: "apiKey" | "routingRule" | "user" | "systemPrompt",
  ) => {
    setSelectedItemId(id);
    setSelectedItemType(type);
    setDeleteDialogOpen(true);
  };

  const toggleApiKeyVisibility = (keyId: string) => {
    setShowApiKey((prev) => ({ ...prev, [keyId]: !prev[keyId] }));
  };

  // Helper functions for missing handlers
  const handleTestTool = async (toolId: string) => {
    try {
      const response = await AuthService.makeAuthenticatedRequest(
        `${import.meta.env.VITE_API_BASE_URL || "http://localhost:3001"}/api/tools/${toolId}/test`,
        { method: "POST" },
      );

      if (response.ok) {
        const result = await response.json();
        toast({
          title: "Tool Test Successful",
          description: `Tool executed successfully: ${result.message || "Test completed"}`,
        });
      } else {
        throw new Error("Tool test failed");
      }
    } catch (error: any) {
      toast({
        title: "Tool Test Failed",
        description: error.message || "Failed to test tool",
        variant: "destructive",
      });
    }
  };

  const handleRefreshKnowledgeBase = async (kbId: string) => {
    try {
      const response = await AuthService.makeAuthenticatedRequest(
        `${import.meta.env.VITE_API_BASE_URL || "http://localhost:3001"}/api/knowledge-bases/${kbId}/reindex`,
        { method: "POST" },
      );

      if (response.ok) {
        toast({
          title: "Reindexing Started",
          description: "Knowledge base reindexing has been initiated",
        });
        // Refresh the knowledge bases list
        await refreshData();
      } else {
        throw new Error("Failed to start reindexing");
      }
    } catch (error: any) {
      toast({
        title: "Reindexing Failed",
        description: error.message || "Failed to start reindexing",
        variant: "destructive",
      });
    }
  };

  const handleEditOrganization = (orgId: string) => {
    // Navigate to organization edit page or open edit modal
    toast({
      title: "Edit Organization",
      description:
        "Organization editing functionality would be implemented here",
    });
  };

  const handleViewUsers = (orgId: string) => {
    // Filter users by organization or navigate to organization users page
    toast({
      title: "View Users",
      description: "Organization users view would be implemented here",
    });
  };

  const handleTestProvider = async (providerId: string) => {
    try {
      const response = await AuthService.makeAuthenticatedRequest(
        `${import.meta.env.VITE_API_BASE_URL || "http://localhost:3001"}/api/providers/${providerId}/test`,
        { method: "POST" },
      );

      if (response.ok) {
        const result = await response.json();
        toast({
          title: "Provider Test Successful",
          description: `Provider is responding correctly: ${result.message || "Connection verified"}`,
        });
      } else {
        throw new Error("Provider test failed");
      }
    } catch (error: any) {
      toast({
        title: "Provider Test Failed",
        description: error.message || "Failed to test provider connection",
        variant: "destructive",
      });
    }
  };

  const handleViewProviderModels = (providerId: string) => {
    const provider = providers.find((p) => p.id === providerId);
    if (provider) {
      toast({
        title: "Provider Models",
        description: `${provider.name} has ${provider.models.length} available models`,
      });
    }
  };

  const handleViewSession = (sessionId: string) => {
    toast({
      title: "View Session",
      description: "Session details view would be implemented here",
    });
  };

  const handleArchiveSession = async (sessionId: string) => {
    try {
      const response = await AuthService.makeAuthenticatedRequest(
        `${import.meta.env.VITE_API_BASE_URL || "http://localhost:3001"}/api/sessions/${sessionId}/archive`,
        { method: "PATCH" },
      );

      if (response.ok) {
        toast({
          title: "Session Archived",
          description: "Session has been successfully archived",
        });
        await refreshData();
      } else {
        throw new Error("Failed to archive session");
      }
    } catch (error: any) {
      toast({
        title: "Archive Failed",
        description: error.message || "Failed to archive session",
        variant: "destructive",
      });
    }
  };

  const handleTerminateSession = async (sessionId: string) => {
    try {
      const response = await AuthService.makeAuthenticatedRequest(
        `${import.meta.env.VITE_API_BASE_URL || "http://localhost:3001"}/api/sessions/${sessionId}/terminate`,
        { method: "DELETE" },
      );

      if (response.ok) {
        toast({
          title: "Session Terminated",
          description: "Session has been successfully terminated",
        });
        await refreshData();
      } else {
        throw new Error("Failed to terminate session");
      }
    } catch (error: any) {
      toast({
        title: "Termination Failed",
        description: error.message || "Failed to terminate session",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <div className="bg-background min-h-screen p-6 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading admin panel...</p>
        </div>
      </div>
    );
  }

  if (error && !AuthService.isAdmin()) {
    return (
      <div className="bg-background min-h-screen p-6 flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
          <p className="text-muted-foreground">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-background min-h-screen p-6">
      <div className="container mx-auto">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold">Admin Panel</h1>
            <p className="text-muted-foreground mt-1">
              Manage your SynapseAI platform configuration and monitor system
              performance
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={refreshData}
              disabled={refreshing}
              className="flex items-center gap-2"
            >
              <RefreshCw
                className={`h-4 w-4 ${refreshing ? "animate-spin" : ""}`}
              />
              {refreshing ? "Refreshing..." : "Refresh"}
            </Button>
            <WebSocketStatusIndicator variant="badge" />
          </div>
        </div>

        <Tabs defaultValue="overview" className="w-full">
          <div className="mb-6">
            <ScrollArea className="w-full whitespace-nowrap">
              <TabsList className="inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground">
                <TabsTrigger
                  value="overview"
                  className="flex items-center gap-2"
                >
                  <BarChart3 size={16} />
                  Overview
                </TabsTrigger>
                <TabsTrigger value="agents" className="flex items-center gap-2">
                  <Bot size={16} />
                  Agents
                </TabsTrigger>
                <TabsTrigger value="tools" className="flex items-center gap-2">
                  <Zap size={16} />
                  Tools
                </TabsTrigger>
                <TabsTrigger
                  value="providers"
                  className="flex items-center gap-2"
                >
                  <Server size={16} />
                  Providers
                </TabsTrigger>
                <TabsTrigger
                  value="sessions"
                  className="flex items-center gap-2"
                >
                  <MessageSquare size={16} />
                  Sessions
                </TabsTrigger>
                <TabsTrigger
                  value="knowledge"
                  className="flex items-center gap-2"
                >
                  <Database size={16} />
                  Knowledge
                </TabsTrigger>
                <TabsTrigger
                  value="analytics"
                  className="flex items-center gap-2"
                >
                  <TrendingUp size={16} />
                  Analytics
                </TabsTrigger>
                <TabsTrigger value="users" className="flex items-center gap-2">
                  <Users size={16} />
                  Users
                </TabsTrigger>
                <TabsTrigger
                  value="organizations"
                  className="flex items-center gap-2"
                >
                  <Building size={16} />
                  Organizations
                </TabsTrigger>
              </TabsList>
            </ScrollArea>
          </div>

          {/* Overview Tab */}
          <TabsContent value="overview">
            <OverviewTab
              users={users}
              systemMetrics={systemMetrics}
              sessions={sessions}
            />
          </TabsContent>

          {/* Agents Tab */}
          <TabsContent value="agents">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Bot className="h-5 w-5" />
                    AI Agents
                  </CardTitle>
                  <CardDescription>
                    Manage AI agents with specialized knowledge and capabilities
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <div className="flex items-center gap-2">
                    <div className="relative">
                      <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search agents..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-8 w-64"
                      />
                    </div>
                    <Select
                      value={filterStatus}
                      onValueChange={setFilterStatus}
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Status</SelectItem>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <Button className="flex items-center gap-2">
                    <PlusCircle size={16} />
                    Create Agent
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[250px]">Agent</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead className="w-[120px]">Model</TableHead>
                        <TableHead className="w-[100px]">Tools</TableHead>
                        <TableHead className="w-[100px]">Status</TableHead>
                        <TableHead className="w-[120px]">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {agents.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-8">
                            <div className="flex flex-col items-center gap-2">
                              <Bot className="h-8 w-8 text-muted-foreground" />
                              <p className="text-muted-foreground">
                                No agents found
                              </p>
                              <Button size="sm" className="mt-2">
                                <PlusCircle className="h-4 w-4 mr-2" />
                                Create your first agent
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : (
                        agents
                          .filter(
                            (agent) =>
                              (filterStatus === "all" ||
                                (filterStatus === "active" && agent.isActive) ||
                                (filterStatus === "inactive" &&
                                  !agent.isActive)) &&
                              (searchTerm === "" ||
                                agent.name
                                  .toLowerCase()
                                  .includes(searchTerm.toLowerCase()) ||
                                agent.description
                                  .toLowerCase()
                                  .includes(searchTerm.toLowerCase())),
                          )
                          .map((agent) => (
                            <TableRow key={agent.id}>
                              <TableCell>
                                <div className="flex items-center gap-3">
                                  <div className="h-8 w-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
                                    {agent.avatar ? (
                                      <img
                                        src={agent.avatar}
                                        alt={agent.name}
                                        className="w-8 h-8 rounded-full"
                                      />
                                    ) : (
                                      <Bot className="h-4 w-4 text-white" />
                                    )}
                                  </div>
                                  <div>
                                    <div className="font-medium">
                                      {agent.name}
                                    </div>
                                    <div className="text-xs text-muted-foreground">
                                      Created{" "}
                                      {new Date(
                                        agent.createdAt,
                                      ).toLocaleDateString()}
                                    </div>
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell className="max-w-md">
                                <div className="truncate">
                                  {agent.description}
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge variant="outline">{agent.model}</Badge>
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center gap-1">
                                  <Zap className="h-3 w-3" />
                                  <span className="text-sm">
                                    {agent.tools.length}
                                  </span>
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge
                                  variant={
                                    agent.isActive ? "default" : "secondary"
                                  }
                                >
                                  {agent.isActive ? "Active" : "Inactive"}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center gap-1">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0"
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0"
                                  >
                                    <TestTube className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0"
                                  >
                                    <Copy className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0 text-destructive"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))
                      )}
                    </TableBody>
                  </Table>
                </div>

                {/* Pagination */}
                {agents.length > 0 && (
                  <div className="flex items-center justify-between px-2 py-4">
                    <div className="text-sm text-muted-foreground">
                      Showing {Math.min(agents.length, itemsPerPage)} of{" "}
                      {agents.length} agents
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm" disabled>
                        <ChevronLeft className="h-4 w-4" />
                        Previous
                      </Button>
                      <Button variant="outline" size="sm" disabled>
                        Next
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Tools Tab */}
          <TabsContent value="tools">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5" />
                    Tools & Integrations
                  </CardTitle>
                  <CardDescription>
                    Manage tools that agents can use to perform actions
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <div className="flex items-center gap-2">
                    <div className="relative">
                      <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search tools..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-8 w-64"
                      />
                    </div>
                    <Select
                      value={filterStatus}
                      onValueChange={setFilterStatus}
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Types</SelectItem>
                        <SelectItem value="function">Function</SelectItem>
                        <SelectItem value="api">API</SelectItem>
                        <SelectItem value="webhook">Webhook</SelectItem>
                        <SelectItem value="database">Database</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <Button className="flex items-center gap-2">
                    <PlusCircle size={16} />
                    Add Tool
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[200px]">Tool</TableHead>
                        <TableHead className="w-[120px]">Type</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead className="w-[100px]">Status</TableHead>
                        <TableHead className="w-[120px]">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {tools.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center py-8">
                            <div className="flex flex-col items-center gap-2">
                              <Zap className="h-8 w-8 text-muted-foreground" />
                              <p className="text-muted-foreground">
                                No tools found
                              </p>
                              <Button size="sm" className="mt-2">
                                <PlusCircle className="h-4 w-4 mr-2" />
                                Add your first tool
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : (
                        tools
                          .filter(
                            (tool) =>
                              (filterStatus === "all" ||
                                tool.type === filterStatus) &&
                              (searchTerm === "" ||
                                tool.name
                                  .toLowerCase()
                                  .includes(searchTerm.toLowerCase()) ||
                                tool.description
                                  .toLowerCase()
                                  .includes(searchTerm.toLowerCase())),
                          )
                          .map((tool) => (
                            <TableRow key={tool.id}>
                              <TableCell>
                                <div className="flex items-center gap-3">
                                  <div className="h-8 w-8 rounded-lg bg-gradient-to-r from-green-500 to-blue-500 flex items-center justify-center">
                                    <Zap className="h-4 w-4 text-white" />
                                  </div>
                                  <div>
                                    <div className="font-medium">
                                      {tool.name}
                                    </div>
                                    <div className="text-xs text-muted-foreground">
                                      Created{" "}
                                      {new Date(
                                        tool.createdAt,
                                      ).toLocaleDateString()}
                                    </div>
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge variant="outline" className="capitalize">
                                  {tool.type}
                                </Badge>
                              </TableCell>
                              <TableCell className="max-w-md">
                                <div className="truncate">
                                  {tool.description}
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge
                                  variant={
                                    tool.isActive ? "default" : "secondary"
                                  }
                                >
                                  {tool.isActive ? "Active" : "Inactive"}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center gap-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0"
                                    onClick={() => openEditDialog(tool, "tool")}
                                  >
                                    <Edit size={16} />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0"
                                    onClick={() => handleTestTool(tool.id)}
                                  >
                                    <TestTube size={16} />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0 text-destructive"
                                    onClick={() =>
                                      openDeleteDialog(tool.id, "tool")
                                    }
                                  >
                                    <Trash2 size={16} />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Knowledge Bases Tab */}
          <TabsContent value="knowledge">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-5 w-5" />
                    Knowledge Bases
                  </CardTitle>
                  <CardDescription>
                    Manage knowledge bases that provide context to agents
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <div className="flex items-center gap-2">
                    <div className="relative">
                      <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search knowledge bases..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-8 w-64"
                      />
                    </div>
                    <Select
                      value={filterStatus}
                      onValueChange={setFilterStatus}
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Status</SelectItem>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="indexing">Indexing</SelectItem>
                        <SelectItem value="error">Error</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <Button className="flex items-center gap-2">
                    <PlusCircle size={16} />
                    Create Knowledge Base
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[250px]">
                          Knowledge Base
                        </TableHead>
                        <TableHead className="w-[120px]">Type</TableHead>
                        <TableHead className="w-[100px]">Documents</TableHead>
                        <TableHead className="w-[100px]">Status</TableHead>
                        <TableHead className="w-[120px]">
                          Last Indexed
                        </TableHead>
                        <TableHead className="w-[120px]">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {knowledgeBases.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-8">
                            <div className="flex flex-col items-center gap-2">
                              <Database className="h-8 w-8 text-muted-foreground" />
                              <p className="text-muted-foreground">
                                No knowledge bases found
                              </p>
                              <Button size="sm" className="mt-2">
                                <PlusCircle className="h-4 w-4 mr-2" />
                                Create your first knowledge base
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : (
                        knowledgeBases
                          .filter(
                            (kb) =>
                              (filterStatus === "all" ||
                                kb.status === filterStatus) &&
                              (searchTerm === "" ||
                                kb.name
                                  .toLowerCase()
                                  .includes(searchTerm.toLowerCase()) ||
                                kb.description
                                  .toLowerCase()
                                  .includes(searchTerm.toLowerCase())),
                          )
                          .map((kb) => (
                            <TableRow key={kb.id}>
                              <TableCell>
                                <div className="flex items-center gap-3">
                                  <div className="h-8 w-8 rounded-lg bg-gradient-to-r from-orange-500 to-red-500 flex items-center justify-center">
                                    <Database className="h-4 w-4 text-white" />
                                  </div>
                                  <div>
                                    <div className="font-medium">{kb.name}</div>
                                    <div className="text-xs text-muted-foreground truncate max-w-[200px]">
                                      {kb.description}
                                    </div>
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge variant="outline" className="capitalize">
                                  {kb.type}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center gap-1">
                                  <FileText className="h-3 w-3" />
                                  <span className="text-sm">
                                    {kb.documentsCount.toLocaleString()}
                                  </span>
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center gap-2">
                                  <Badge
                                    variant={
                                      kb.status === "active"
                                        ? "default"
                                        : kb.status === "indexing"
                                          ? "secondary"
                                          : kb.status === "error"
                                            ? "destructive"
                                            : "outline"
                                    }
                                  >
                                    {kb.status}
                                  </Badge>
                                  {kb.status === "indexing" && (
                                    <Loader2 className="h-3 w-3 animate-spin" />
                                  )}
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="text-sm">
                                  {kb.lastIndexed
                                    ? new Date(
                                        kb.lastIndexed,
                                      ).toLocaleDateString()
                                    : "Never"}
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center gap-1">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0"
                                    onClick={() =>
                                      openEditDialog(kb, "knowledgeBase")
                                    }
                                  >
                                    <Edit size={16} />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0"
                                    onClick={() =>
                                      handleRefreshKnowledgeBase(kb.id)
                                    }
                                  >
                                    <RefreshCw size={16} />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0"
                                  >
                                    <Upload size={16} />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0 text-destructive"
                                    onClick={() =>
                                      openDeleteDialog(kb.id, "knowledgeBase")
                                    }
                                  >
                                    <Trash2 size={16} />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics">
            <div className="grid gap-6">
              {/* Usage Analytics */}
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Total Requests
                    </CardTitle>
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {performanceMetrics
                        .reduce((sum, metric) => sum + metric.totalQueries, 0)
                        .toLocaleString()}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      +12% from last month
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Avg Response Time
                    </CardTitle>
                    <Clock className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {(
                        performanceMetrics.reduce(
                          (sum, metric) => sum + metric.responseTime,
                          0,
                        ) / performanceMetrics.length
                      ).toFixed(1)}
                      s
                    </div>
                    <p className="text-xs text-muted-foreground">
                      -5% from last month
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Success Rate
                    </CardTitle>
                    <CheckCircle className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {(
                        performanceMetrics.reduce(
                          (sum, metric) => sum + metric.successRate,
                          0,
                        ) / performanceMetrics.length
                      ).toFixed(1)}
                      %
                    </div>
                    <p className="text-xs text-muted-foreground">
                      +2% from last month
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Total Cost
                    </CardTitle>
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      $
                      {performanceMetrics
                        .reduce(
                          (sum, metric) =>
                            sum + metric.costPerQuery * metric.totalQueries,
                          0,
                        )
                        .toFixed(2)}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      +8% from last month
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Provider Performance */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Provider Performance</CardTitle>
                      <CardDescription>
                        Compare performance metrics across AI providers
                      </CardDescription>
                    </div>
                    <Button
                      variant="outline"
                      className="flex items-center gap-2"
                    >
                      <RefreshCw size={16} />
                      Refresh Data
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-medium mb-4">
                        Response Time (seconds)
                      </h3>
                      <div className="space-y-4">
                        {performanceMetrics.map((metric) => (
                          <div
                            key={`rt-${metric.provider}`}
                            className="space-y-1"
                          >
                            <div className="flex items-center justify-between">
                              <span>{metric.provider}</span>
                              <span className="text-sm text-muted-foreground">
                                {metric.responseTime}s
                              </span>
                            </div>
                            <Progress
                              value={100 - (metric.responseTime / 2) * 100}
                            />
                          </div>
                        ))}
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h3 className="text-lg font-medium mb-4">
                        Success Rate (%)
                      </h3>
                      <div className="space-y-4">
                        {performanceMetrics.map((metric) => (
                          <div
                            key={`sr-${metric.provider}`}
                            className="space-y-1"
                          >
                            <div className="flex items-center justify-between">
                              <span>{metric.provider}</span>
                              <span className="text-sm text-muted-foreground">
                                {metric.successRate}%
                              </span>
                            </div>
                            <Progress value={metric.successRate} />
                          </div>
                        ))}
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h3 className="text-lg font-medium mb-4">
                        Cost Per Query ($)
                      </h3>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Provider</TableHead>
                            <TableHead>Cost Per Query</TableHead>
                            <TableHead>Total Queries</TableHead>
                            <TableHead>Estimated Total Cost</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {performanceMetrics.map((metric) => (
                            <TableRow key={`cost-${metric.provider}`}>
                              <TableCell>{metric.provider}</TableCell>
                              <TableCell>
                                ${metric.costPerQuery.toFixed(4)}
                              </TableCell>
                              <TableCell>
                                {metric.totalQueries.toLocaleString()}
                              </TableCell>
                              <TableCell>
                                $
                                {(
                                  metric.costPerQuery * metric.totalQueries
                                ).toFixed(2)}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>

                    <div className="bg-muted/50 p-4 rounded-lg flex items-center gap-3">
                      <AlertTriangle className="text-amber-500" />
                      <p className="text-sm">
                        Performance data is updated every 15 minutes. Last
                        update: 2023-06-15 16:30:00
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Usage Trends */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5" />
                    Usage Trends
                  </CardTitle>
                  <CardDescription>
                    Track usage patterns and trends over time
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px] flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
                    <div className="text-center">
                      <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground">
                        Usage analytics chart would be displayed here
                      </p>
                      <p className="text-sm text-muted-foreground mt-2">
                        Integration with charting library needed
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Organizations Tab */}
          <TabsContent value="organizations">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Building className="h-5 w-5" />
                    Organizations
                  </CardTitle>
                  <CardDescription>
                    Manage organizations and their settings
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <div className="flex items-center gap-2">
                    <div className="relative">
                      <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search organizations..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-8 w-64"
                      />
                    </div>
                  </div>
                  <Button className="flex items-center gap-2">
                    <PlusCircle size={16} />
                    Create Organization
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[250px]">
                          Organization
                        </TableHead>
                        <TableHead className="w-[150px]">Slug</TableHead>
                        <TableHead className="w-[100px]">Users</TableHead>
                        <TableHead className="w-[120px]">Max Users</TableHead>
                        <TableHead className="w-[120px]">Created</TableHead>
                        <TableHead className="w-[120px]">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {organizations.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-8">
                            <div className="flex flex-col items-center gap-2">
                              <Building className="h-8 w-8 text-muted-foreground" />
                              <p className="text-muted-foreground">
                                No organizations found
                              </p>
                              <Button size="sm" className="mt-2">
                                <PlusCircle className="h-4 w-4 mr-2" />
                                Create your first organization
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : (
                        organizations
                          .filter(
                            (org) =>
                              searchTerm === "" ||
                              org.name
                                .toLowerCase()
                                .includes(searchTerm.toLowerCase()) ||
                              org.slug
                                .toLowerCase()
                                .includes(searchTerm.toLowerCase()),
                          )
                          .map((org) => (
                            <TableRow key={org.id}>
                              <TableCell>
                                <div className="flex items-center gap-3">
                                  <div className="h-8 w-8 rounded-lg bg-gradient-to-r from-indigo-500 to-purple-500 flex items-center justify-center">
                                    <Building className="h-4 w-4 text-white" />
                                  </div>
                                  <div>
                                    <div className="font-medium">
                                      {org.name}
                                    </div>
                                    <div className="text-xs text-muted-foreground">
                                      {org.settings.features.length} features
                                    </div>
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <code className="text-sm bg-muted px-2 py-1 rounded">
                                  {org.slug}
                                </code>
                              </TableCell>
                              <TableCell>
                                <div className="text-sm">
                                  {
                                    users.filter(
                                      (u) => u.organizationId === org.id,
                                    ).length
                                  }
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="text-sm">
                                  {org.settings.maxUsers === -1
                                    ? "Unlimited"
                                    : org.settings.maxUsers.toLocaleString()}
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="text-sm">
                                  {new Date(org.createdAt).toLocaleDateString()}
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center gap-1">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0"
                                    onClick={() =>
                                      openEditDialog(org, "organization")
                                    }
                                  >
                                    <Edit size={16} />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0"
                                    onClick={() =>
                                      handleEditOrganization(org.id)
                                    }
                                  >
                                    <Settings size={16} />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0"
                                    onClick={() => handleViewUsers(org.id)}
                                  >
                                    <Users size={16} />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0 text-destructive"
                                    onClick={() =>
                                      openDeleteDialog(org.id, "organization")
                                    }
                                  >
                                    <Trash2 size={16} />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* API Keys Tab */}
          <TabsContent value="api-keys">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>API Keys</CardTitle>
                  <CardDescription>
                    Manage API keys for different AI providers
                  </CardDescription>
                </div>
                <Dialog
                  open={apiKeyDialogOpen}
                  onOpenChange={setApiKeyDialogOpen}
                >
                  <DialogTrigger asChild>
                    <Button className="flex items-center gap-2">
                      <PlusCircle size={16} />
                      Add API Key
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>
                        {editingItem ? "Edit API Key" : "Add New API Key"}
                      </DialogTitle>
                      <DialogDescription>
                        {editingItem
                          ? "Update the API key details."
                          : "Enter the details for the new API key. The key will be encrypted before storage."}
                      </DialogDescription>
                    </DialogHeader>
                    {error && (
                      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
                        {error}
                      </div>
                    )}
                    <div className="grid gap-4 py-4">
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="name" className="text-right">
                          Name
                        </Label>
                        <Input
                          id="name"
                          placeholder="Production Key"
                          className="col-span-3"
                          value={apiKeyForm.name}
                          onChange={(e) =>
                            setApiKeyForm((prev) => ({
                              ...prev,
                              name: e.target.value,
                            }))
                          }
                        />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="provider" className="text-right">
                          Provider
                        </Label>
                        <Select
                          value={apiKeyForm.provider}
                          onValueChange={(value) =>
                            setApiKeyForm((prev) => ({
                              ...prev,
                              provider: value,
                            }))
                          }
                          disabled={!!editingItem}
                        >
                          <SelectTrigger className="col-span-3">
                            <SelectValue placeholder="Select provider" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="openai">OpenAI</SelectItem>
                            <SelectItem value="claude">Claude</SelectItem>
                            <SelectItem value="gemini">Gemini</SelectItem>
                            <SelectItem value="mistral">Mistral</SelectItem>
                            <SelectItem value="groq">Groq</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="api-key" className="text-right">
                          API Key
                        </Label>
                        <Input
                          id="api-key"
                          type="password"
                          placeholder={
                            editingItem
                              ? "Leave empty to keep current key"
                              : "sk-..."
                          }
                          className="col-span-3"
                          value={apiKeyForm.key}
                          onChange={(e) =>
                            setApiKeyForm((prev) => ({
                              ...prev,
                              key: e.target.value,
                            }))
                          }
                        />
                      </div>
                    </div>
                    <DialogFooter>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setApiKeyDialogOpen(false);
                          setEditingItem(null);
                          setApiKeyForm({ provider: "", name: "", key: "" });
                          setError(null);
                        }}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={
                          editingItem ? handleEditApiKey : handleAddApiKey
                        }
                      >
                        {editingItem ? "Update Key" : "Add Key"}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Provider</TableHead>
                      <TableHead>Key</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Last Used</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {apiKeys.map((key) => (
                      <TableRow key={key.id}>
                        <TableCell className="font-medium">
                          {key.name}
                        </TableCell>
                        <TableCell>{key.provider}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <span className="font-mono text-sm">
                              {showApiKey[key.id]
                                ? key.encryptedKey
                                : ApiKeyManagementService.getMaskedKey(
                                    key.encryptedKey,
                                  )}
                            </span>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0"
                              onClick={() => toggleApiKeyVisibility(key.id)}
                            >
                              {showApiKey[key.id] ? (
                                <EyeOff size={12} />
                              ) : (
                                <Eye size={12} />
                              )}
                            </Button>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Badge
                              variant={
                                key.status === "active"
                                  ? "default"
                                  : "secondary"
                              }
                            >
                              {key.status}
                            </Badge>
                            {testResults[key.id] && (
                              <div className="flex items-center gap-1">
                                {testResults[key.id].success ? (
                                  <CheckCircle className="h-4 w-4 text-green-500" />
                                ) : (
                                  <XCircle className="h-4 w-4 text-red-500" />
                                )}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {key.lastUsed
                            ? new Date(key.lastUsed).toLocaleString()
                            : "Never"}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => openEditDialog(key, "apiKey")}
                            >
                              <Edit size={16} />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => handleTestApiKey(key.id)}
                              disabled={testingApiKey === key.id}
                            >
                              {testingApiKey === key.id ? (
                                <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                              ) : (
                                <TestTube size={16} />
                              )}
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 text-destructive"
                              onClick={() => openDeleteDialog(key.id, "apiKey")}
                            >
                              <Trash2 size={16} />
                            </Button>
                            <Switch
                              checked={key.status === "active"}
                              onCheckedChange={() =>
                                handleToggleStatus(key.id, "apiKey")
                              }
                            />
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Routing Rules Tab */}
          <TabsContent value="routing">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Routing Rules</CardTitle>
                  <CardDescription>
                    Configure how queries are routed to different AI providers
                  </CardDescription>
                </div>
                <Dialog
                  open={routingRuleDialogOpen}
                  onOpenChange={setRoutingRuleDialogOpen}
                >
                  <DialogTrigger asChild>
                    <Button className="flex items-center gap-2">
                      <PlusCircle size={16} />
                      Add Rule
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>
                        {editingItem ? "Edit Routing Rule" : "Add Routing Rule"}
                      </DialogTitle>
                      <DialogDescription>
                        {editingItem
                          ? "Update the routing rule details."
                          : "Create a new rule to route specific queries to a particular AI provider."}
                      </DialogDescription>
                    </DialogHeader>
                    {error && (
                      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
                        {error}
                      </div>
                    )}
                    <div className="grid gap-4 py-4">
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="rule-name" className="text-right">
                          Name
                        </Label>
                        <Input
                          id="rule-name"
                          placeholder="Technical Queries"
                          className="col-span-3"
                          value={routingRuleForm.name}
                          onChange={(e) =>
                            setRoutingRuleForm((prev) => ({
                              ...prev,
                              name: e.target.value,
                            }))
                          }
                        />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="priority" className="text-right">
                          Priority
                        </Label>
                        <Input
                          id="priority"
                          type="number"
                          placeholder="1"
                          className="col-span-3"
                          value={routingRuleForm.priority}
                          onChange={(e) =>
                            setRoutingRuleForm((prev) => ({
                              ...prev,
                              priority: parseInt(e.target.value) || 1,
                            }))
                          }
                        />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="condition" className="text-right">
                          Condition
                        </Label>
                        <Input
                          id="condition"
                          placeholder='contains("code", "programming")'
                          className="col-span-3"
                          value={routingRuleForm.condition}
                          onChange={(e) =>
                            setRoutingRuleForm((prev) => ({
                              ...prev,
                              condition: e.target.value,
                            }))
                          }
                        />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="rule-provider" className="text-right">
                          Provider
                        </Label>
                        <Select
                          value={routingRuleForm.provider}
                          onValueChange={(value) =>
                            setRoutingRuleForm((prev) => ({
                              ...prev,
                              provider: value,
                            }))
                          }
                        >
                          <SelectTrigger className="col-span-3">
                            <SelectValue placeholder="Select provider" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="openai">OpenAI</SelectItem>
                            <SelectItem value="claude">Claude</SelectItem>
                            <SelectItem value="gemini">Gemini</SelectItem>
                            <SelectItem value="mistral">Mistral</SelectItem>
                            <SelectItem value="groq">Groq</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setRoutingRuleDialogOpen(false);
                          setEditingItem(null);
                          setRoutingRuleForm({
                            name: "",
                            priority: 1,
                            condition: "",
                            provider: "",
                          });
                          setError(null);
                        }}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={
                          editingItem
                            ? handleEditRoutingRule
                            : handleAddRoutingRule
                        }
                      >
                        {editingItem ? "Update Rule" : "Add Rule"}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Priority</TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Condition</TableHead>
                      <TableHead>Provider</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {routingRules.map((rule) => (
                      <TableRow key={rule.id}>
                        <TableCell>{rule.priority}</TableCell>
                        <TableCell className="font-medium">
                          {rule.name}
                        </TableCell>
                        <TableCell className="max-w-md truncate">
                          {rule.condition}
                        </TableCell>
                        <TableCell>{rule.provider}</TableCell>
                        <TableCell>
                          <Badge
                            variant={rule.isActive ? "default" : "secondary"}
                          >
                            {rule.isActive ? "active" : "inactive"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() =>
                                openEditDialog(rule, "routingRule")
                              }
                            >
                              <Edit size={16} />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => handleTestRoutingRule(rule.id)}
                            >
                              <TestTube size={16} />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 text-destructive"
                              onClick={() =>
                                openDeleteDialog(rule.id, "routingRule")
                              }
                            >
                              <Trash2 size={16} />
                            </Button>
                            <Switch
                              checked={rule.isActive}
                              onCheckedChange={() =>
                                handleToggleStatus(rule.id, "routingRule")
                              }
                            />
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Performance Tab */}
          <TabsContent value="performance">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>System Performance</CardTitle>
                    <CardDescription>
                      Monitor the performance of different AI providers
                    </CardDescription>
                  </div>
                  <Button variant="outline" className="flex items-center gap-2">
                    <RefreshCw size={16} />
                    Refresh Data
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-8">
                  <div>
                    <h3 className="text-lg font-medium mb-4">
                      Response Time (seconds)
                    </h3>
                    <div className="space-y-4">
                      {performanceMetrics.map((metric) => (
                        <div
                          key={`rt-${metric.provider}`}
                          className="space-y-1"
                        >
                          <div className="flex items-center justify-between">
                            <span>{metric.provider}</span>
                            <span className="text-sm text-muted-foreground">
                              {metric.responseTime}s
                            </span>
                          </div>
                          <Progress
                            value={100 - (metric.responseTime / 2) * 100}
                          />
                        </div>
                      ))}
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <h3 className="text-lg font-medium mb-4">
                      Success Rate (%)
                    </h3>
                    <div className="space-y-4">
                      {performanceMetrics.map((metric) => (
                        <div
                          key={`sr-${metric.provider}`}
                          className="space-y-1"
                        >
                          <div className="flex items-center justify-between">
                            <span>{metric.provider}</span>
                            <span className="text-sm text-muted-foreground">
                              {metric.successRate}%
                            </span>
                          </div>
                          <Progress value={metric.successRate} />
                        </div>
                      ))}
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <h3 className="text-lg font-medium mb-4">
                      Cost Per Query ($)
                    </h3>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Provider</TableHead>
                          <TableHead>Cost Per Query</TableHead>
                          <TableHead>Total Queries</TableHead>
                          <TableHead>Estimated Total Cost</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {performanceMetrics.map((metric) => (
                          <TableRow key={`cost-${metric.provider}`}>
                            <TableCell>{metric.provider}</TableCell>
                            <TableCell>
                              ${metric.costPerQuery.toFixed(4)}
                            </TableCell>
                            <TableCell>
                              {metric.totalQueries.toLocaleString()}
                            </TableCell>
                            <TableCell>
                              $
                              {(
                                metric.costPerQuery * metric.totalQueries
                              ).toFixed(2)}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>

                  <div className="bg-muted/50 p-4 rounded-lg flex items-center gap-3">
                    <AlertTriangle className="text-amber-500" />
                    <p className="text-sm">
                      Performance data is updated every 15 minutes. Last update:
                      2023-06-15 16:30:00
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* User Management Tab */}
          <TabsContent value="users">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>User Management</CardTitle>
                  <CardDescription>
                    Manage user access and permissions
                  </CardDescription>
                </div>
                <Dialog open={userDialogOpen} onOpenChange={setUserDialogOpen}>
                  <DialogTrigger asChild>
                    <Button className="flex items-center gap-2">
                      <PlusCircle size={16} />
                      Add User
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>
                        {editingItem ? "Edit User" : "Add New User"}
                      </DialogTitle>
                      <DialogDescription>
                        {editingItem
                          ? "Update the user details."
                          : "Create a new user account with specified role and permissions."}
                      </DialogDescription>
                    </DialogHeader>
                    {error && (
                      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
                        {error}
                      </div>
                    )}
                    <div className="grid gap-4 py-4">
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="user-name" className="text-right">
                          Name
                        </Label>
                        <Input
                          id="user-name"
                          placeholder="John Doe"
                          className="col-span-3"
                          value={userForm.name}
                          onChange={(e) =>
                            setUserForm((prev) => ({
                              ...prev,
                              name: e.target.value,
                            }))
                          }
                        />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="user-email" className="text-right">
                          Email
                        </Label>
                        <Input
                          id="user-email"
                          type="email"
                          placeholder="<EMAIL>"
                          className="col-span-3"
                          value={userForm.email}
                          onChange={(e) =>
                            setUserForm((prev) => ({
                              ...prev,
                              email: e.target.value,
                            }))
                          }
                        />
                      </div>
                      {!editingItem && (
                        <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="user-password" className="text-right">
                            Password
                          </Label>
                          <Input
                            id="user-password"
                            type="password"
                            placeholder="••••••••"
                            className="col-span-3"
                            value={userForm.password}
                            onChange={(e) =>
                              setUserForm((prev) => ({
                                ...prev,
                                password: e.target.value,
                              }))
                            }
                          />
                        </div>
                      )}
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="user-role" className="text-right">
                          Role
                        </Label>
                        <Select
                          value={userForm.role}
                          onValueChange={(value: "admin" | "user") =>
                            setUserForm((prev) => ({ ...prev, role: value }))
                          }
                        >
                          <SelectTrigger className="col-span-3">
                            <SelectValue placeholder="Select role" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="user">User</SelectItem>
                            <SelectItem value="admin">Administrator</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setUserDialogOpen(false);
                          setEditingItem(null);
                          setUserForm({
                            name: "",
                            email: "",
                            password: "",
                            role: "user",
                          });
                          setError(null);
                        }}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={editingItem ? handleEditUser : handleAddUser}
                      >
                        {editingItem ? "Update User" : "Add User"}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[500px]">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead>Role</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Last Active</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {users.map((user) => (
                        <TableRow key={user.id}>
                          <TableCell className="font-medium">
                            {user.name}
                          </TableCell>
                          <TableCell>{user.email}</TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                user.role === "admin" ? "default" : "secondary"
                              }
                            >
                              {user.role}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                user.status === "active"
                                  ? "default"
                                  : "secondary"
                              }
                            >
                              {user.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {user.lastActive
                              ? new Date(user.lastActive).toLocaleString()
                              : "Never"}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0"
                                onClick={() => openEditDialog(user, "user")}
                              >
                                <Edit size={16} />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0 text-destructive"
                                onClick={() =>
                                  openDeleteDialog(user.id, "user")
                                }
                                disabled={AuthService.getUser()?.id === user.id}
                              >
                                <Trash2 size={16} />
                              </Button>
                              <Switch
                                checked={user.status === "active"}
                                onCheckedChange={() =>
                                  handleToggleStatus(user.id, "user")
                                }
                                disabled={AuthService.getUser()?.id === user.id}
                              />
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>

          {/* System Prompts Tab */}
          <TabsContent value="system-prompts">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>System Prompts</CardTitle>
                  <CardDescription>
                    Manage system prompts that define AI behavior and
                    personality
                  </CardDescription>
                </div>
                <Dialog
                  open={systemPromptDialogOpen}
                  onOpenChange={setSystemPromptDialogOpen}
                >
                  <DialogTrigger asChild>
                    <Button className="flex items-center gap-2">
                      <PlusCircle size={16} />
                      Add System Prompt
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl">
                    <DialogHeader>
                      <DialogTitle>
                        {editingItem
                          ? "Edit System Prompt"
                          : "Add System Prompt"}
                      </DialogTitle>
                      <DialogDescription>
                        {editingItem
                          ? "Update the system prompt details."
                          : "Create a new system prompt to define AI behavior in specific contexts."}
                      </DialogDescription>
                    </DialogHeader>
                    {error && (
                      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
                        {error}
                      </div>
                    )}
                    <div className="grid gap-4 py-4">
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="prompt-name" className="text-right">
                          Name
                        </Label>
                        <Input
                          id="prompt-name"
                          placeholder="Creative Writing Assistant"
                          className="col-span-3"
                          value={systemPromptForm.name}
                          onChange={(e) =>
                            setSystemPromptForm((prev) => ({
                              ...prev,
                              name: e.target.value,
                            }))
                          }
                        />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="prompt-context" className="text-right">
                          Context
                        </Label>
                        <Select
                          value={systemPromptForm.context}
                          onValueChange={(value: any) =>
                            setSystemPromptForm((prev) => ({
                              ...prev,
                              context: value,
                            }))
                          }
                        >
                          <SelectTrigger className="col-span-3">
                            <SelectValue placeholder="Select context" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="general">General</SelectItem>
                            <SelectItem value="technical">Technical</SelectItem>
                            <SelectItem value="creative">Creative</SelectItem>
                            <SelectItem value="support">Support</SelectItem>
                            <SelectItem value="custom">Custom</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="grid grid-cols-4 items-start gap-4">
                        <Label
                          htmlFor="prompt-content"
                          className="text-right pt-2"
                        >
                          Content
                        </Label>
                        <Textarea
                          id="prompt-content"
                          placeholder="You are a helpful AI assistant that..."
                          className="col-span-3 min-h-[200px]"
                          value={systemPromptForm.content}
                          onChange={(e) =>
                            setSystemPromptForm((prev) => ({
                              ...prev,
                              content: e.target.value,
                            }))
                          }
                        />
                      </div>
                    </div>
                    <DialogFooter>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setSystemPromptDialogOpen(false);
                          setEditingItem(null);
                          setSystemPromptForm({
                            name: "",
                            content: "",
                            context: "general",
                          });
                          setError(null);
                        }}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={
                          editingItem
                            ? handleEditSystemPrompt
                            : handleAddSystemPrompt
                        }
                      >
                        {editingItem ? "Update Prompt" : "Add Prompt"}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Context</TableHead>
                      <TableHead>Content Preview</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {systemPrompts.map((prompt) => (
                      <TableRow key={prompt.id}>
                        <TableCell className="font-medium">
                          {prompt.name}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{prompt.context}</Badge>
                        </TableCell>
                        <TableCell className="max-w-md">
                          <div className="truncate text-sm text-muted-foreground">
                            {prompt.content.substring(0, 100)}
                            {prompt.content.length > 100 && "..."}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Badge
                              variant={
                                prompt.isActive ? "default" : "secondary"
                              }
                            >
                              {prompt.isActive ? "active" : "inactive"}
                            </Badge>
                            {testResults[prompt.id] && (
                              <div className="flex items-center gap-1">
                                {testResults[prompt.id].success ? (
                                  <CheckCircle className="h-4 w-4 text-green-500" />
                                ) : (
                                  <XCircle className="h-4 w-4 text-red-500" />
                                )}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {new Date(prompt.createdAt).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() =>
                                openEditDialog(prompt, "systemPrompt")
                              }
                            >
                              <Edit size={16} />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => handleTestSystemPrompt(prompt.id)}
                              disabled={testingSystemPrompt === prompt.id}
                            >
                              {testingSystemPrompt === prompt.id ? (
                                <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                              ) : (
                                <TestTube size={16} />
                              )}
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 text-destructive"
                              onClick={() =>
                                openDeleteDialog(prompt.id, "systemPrompt")
                              }
                            >
                              <Trash2 size={16} />
                            </Button>
                            <Switch
                              checked={prompt.isActive}
                              onCheckedChange={() =>
                                handleToggleStatus(prompt.id, "systemPrompt")
                              }
                            />
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Global Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete this{" "}
              {selectedItemType?.replace(/([A-Z])/g, " $1").toLowerCase()}.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              className="bg-destructive text-destructive-foreground"
              onClick={handleDeleteItem}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default AdminPanel;
function setDeployments(arg0: any) {
  throw new Error("Function not implemented.");
}

