import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  PlusCircle,
  Trash2,
  Edit,
  Zap,
  Search,
  RefreshCw,
  TestTube,
  Globe,
  Database,
  Webhook,
  Code,
} from "lucide-react";
import { Tool } from "@/types";

interface ToolsTabProps {
  tools: Tool[];
  onRefresh: () => void;
  onToggleStatus: (id: string, type: string) => void;
  onDeleteItem: (id: string, type: string) => void;
  searchTerm: string;
  onSearchChange: (value: string) => void;
  filterStatus: string;
  onFilterChange: (value: string) => void;
}

const ToolsTab: React.FC<ToolsTabProps> = ({
  tools,
  onRefresh,
  onToggleStatus,
  onDeleteItem,
  searchTerm,
  onSearchChange,
  filterStatus,
  onFilterChange,
}) => {
  const [toolDialogOpen, setToolDialogOpen] = useState(false);
  const [selectedTool, setSelectedTool] = useState<Tool | null>(null);

  const filteredTools = tools.filter((tool) => {
    const matchesSearch = tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tool.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === "all" || 
                         (filterStatus === "active" && tool.isActive) ||
                         (filterStatus === "inactive" && !tool.isActive);
    return matchesSearch && matchesFilter;
  });

  const handleCreateTool = () => {
    setSelectedTool(null);
    setToolDialogOpen(true);
  };

  const handleEditTool = (tool: Tool) => {
    setSelectedTool(tool);
    setToolDialogOpen(true);
  };

  const handleTestTool = (tool: Tool) => {
    // TODO: Implement tool testing functionality
    console.log("Testing tool:", tool.name);
  };

  const getToolIcon = (type: string) => {
    switch (type) {
      case "api":
        return <Globe className="h-4 w-4" />;
      case "webhook":
        return <Webhook className="h-4 w-4" />;
      case "database":
        return <Database className="h-4 w-4" />;
      case "function":
        return <Code className="h-4 w-4" />;
      default:
        return <Zap className="h-4 w-4" />;
    }
  };

  const getToolTypeColor = (type: string) => {
    switch (type) {
      case "api":
        return "bg-blue-100 text-blue-800";
      case "webhook":
        return "bg-purple-100 text-purple-800";
      case "database":
        return "bg-green-100 text-green-800";
      case "function":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Tools
          </CardTitle>
          <CardDescription>
            Manage custom tools and integrations for AI agents
          </CardDescription>
        </div>
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search tools..."
                value={searchTerm}
                onChange={(e) => onSearchChange(e.target.value)}
                className="pl-8 w-[200px]"
              />
            </div>
            <select
              value={filterStatus}
              onChange={(e) => onFilterChange(e.target.value)}
              className="px-3 py-2 border border-input bg-background rounded-md text-sm"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
          <Button variant="outline" size="sm" onClick={onRefresh}>
            <RefreshCw className="h-4 w-4" />
          </Button>
          <Button size="sm" onClick={handleCreateTool}>
            <PlusCircle className="h-4 w-4 mr-2" />
            Create Tool
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredTools.map((tool) => (
              <TableRow key={tool.id}>
                <TableCell className="font-medium">
                  <div className="flex items-center gap-2">
                    <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                      {getToolIcon(tool.type)}
                    </div>
                    {tool.name}
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant="outline" className={getToolTypeColor(tool.type)}>
                    {tool.type}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="max-w-[300px] truncate" title={tool.description}>
                    {tool.description}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={tool.isActive}
                      onCheckedChange={() => onToggleStatus(tool.id, "tool")}
                    />
                    <Badge
                      variant={tool.isActive ? "default" : "secondary"}
                      className={
                        tool.isActive
                          ? "bg-green-100 text-green-800"
                          : "bg-gray-100 text-gray-800"
                      }
                    >
                      {tool.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                </TableCell>
                <TableCell>
                  {new Date(tool.createdAt).toLocaleDateString()}
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex items-center justify-end gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleTestTool(tool)}
                    >
                      <TestTube className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditTool(tool)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete Tool</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to delete "{tool.name}"? This action cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            className="bg-destructive text-destructive-foreground"
                            onClick={() => onDeleteItem(tool.id, "tool")}
                          >
                            Delete
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        {filteredTools.length === 0 && (
          <div className="text-center py-8">
            <Zap className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No tools found</h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm || filterStatus !== "all"
                ? "No tools match your current filters."
                : "Get started by creating your first custom tool."}
            </p>
            {(!searchTerm && filterStatus === "all") && (
              <Button onClick={handleCreateTool}>
                <PlusCircle className="h-4 w-4 mr-2" />
                Create Tool
              </Button>
            )}
          </div>
        )}
      </CardContent>

      {/* Tool Creation/Edit Dialog */}
      <Dialog open={toolDialogOpen} onOpenChange={setToolDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {selectedTool ? "Edit Tool" : "Create New Tool"}
            </DialogTitle>
            <DialogDescription>
              {selectedTool 
                ? "Update the tool configuration and settings."
                : "Configure your new custom tool for AI agents."
              }
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {/* TODO: Add tool form fields */}
            <p className="text-sm text-muted-foreground">
              Tool creation form will be implemented here.
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setToolDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => setToolDialogOpen(false)}>
              {selectedTool ? "Update Tool" : "Create Tool"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default ToolsTab;
