import React from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  TrendingUp,
  TrendingDown,
  Activity,
  Users,
  MessageSquare,
  DollarSign,
  Clock,
  Zap,
  Bot,
  Database,
} from "lucide-react";
import { SystemMetrics, Session, User } from "@/types";

interface AnalyticsTabProps {
  systemMetrics: SystemMetrics | null;
  sessions: Session[];
  users: User[];
}

const AnalyticsTab: React.FC<AnalyticsTabProps> = ({
  systemMetrics,
  sessions,
  users,
}) => {
  // Calculate derived analytics
  const activeSessions = sessions.filter(s => s.status === "active").length;
  const totalMessages = sessions.reduce((sum, s) => sum + s.messageCount, 0);
  const totalCost = sessions.reduce((sum, s) => sum + s.cost, 0);
  const avgMessagesPerSession = sessions.length > 0 ? totalMessages / sessions.length : 0;

  // Mock provider usage data (in production, this would come from real analytics)
  const providerUsage = [
    { name: "OpenAI", usage: 45, cost: 234.56, color: "bg-blue-500" },
    { name: "Claude", usage: 30, cost: 189.23, color: "bg-purple-500" },
    { name: "Gemini", usage: 15, cost: 98.45, color: "bg-green-500" },
    { name: "Mistral", usage: 7, cost: 45.67, color: "bg-orange-500" },
    { name: "Groq", usage: 3, cost: 12.34, color: "bg-red-500" },
  ];

  // Mock time series data for charts
  const usageData = [
    { period: "Last 7 days", messages: 1234, users: 89, cost: 45.67 },
    { period: "Last 30 days", messages: 5678, users: 234, cost: 189.23 },
    { period: "Last 90 days", messages: 15432, users: 567, cost: 456.78 },
  ];

  const metrics = systemMetrics || {
    totalUsers: users.length,
    activeUsers: users.filter(u => {
      const lastActive = new Date(u.lastActive);
      const now = new Date();
      return (now.getTime() - lastActive.getTime()) < (24 * 60 * 60 * 1000);
    }).length,
    totalSessions: sessions.length,
    activeSessions,
    totalMessages,
    totalTokens: totalMessages * 150,
    totalCost,
    averageResponseTime: 1.2,
    errorRate: 0.02,
    uptime: 99.8,
    lastUpdated: new Date(),
  };

  return (
    <div className="grid gap-6">
      {/* Key Performance Indicators */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Messages</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalMessages.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+12%</span> from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.activeUsers}</div>
            <p className="text-xs text-muted-foreground">
              {metrics.totalUsers} total users
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Response Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.averageResponseTime}s</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">-0.2s</span> improvement
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Cost</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${metrics.totalCost.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">
              {metrics.totalTokens.toLocaleString()} tokens
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Provider Usage and Performance */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bot className="h-5 w-5" />
              AI Provider Usage
            </CardTitle>
            <CardDescription>
              Distribution of requests across AI providers
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {providerUsage.map((provider) => (
              <div key={provider.name} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className={`w-3 h-3 rounded-full ${provider.color}`} />
                    <span className="text-sm font-medium">{provider.name}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">{provider.usage}%</div>
                    <div className="text-xs text-muted-foreground">
                      ${provider.cost.toFixed(2)}
                    </div>
                  </div>
                </div>
                <Progress value={provider.usage} className="h-2" />
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              System Health
            </CardTitle>
            <CardDescription>
              Current system performance metrics
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Uptime</span>
                <span className="text-sm text-muted-foreground">{metrics.uptime}%</span>
              </div>
              <Progress value={metrics.uptime} className="h-2" />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Success Rate</span>
                <span className="text-sm text-muted-foreground">
                  {((1 - metrics.errorRate) * 100).toFixed(1)}%
                </span>
              </div>
              <Progress value={(1 - metrics.errorRate) * 100} className="h-2" />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Performance</span>
                <span className="text-sm text-muted-foreground">Excellent</span>
              </div>
              <Progress value={85} className="h-2" />
            </div>

            <div className="pt-2 border-t">
              <div className="flex items-center justify-between text-sm">
                <span>Active Sessions</span>
                <Badge variant="secondary">{metrics.activeSessions}</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Usage Trends */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Usage Trends
          </CardTitle>
          <CardDescription>
            Historical usage patterns and growth metrics
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            {usageData.map((period, index) => (
              <div key={period.period} className="space-y-3">
                <div className="text-sm font-medium text-muted-foreground">
                  {period.period}
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <MessageSquare className="h-4 w-4 text-blue-500" />
                      <span className="text-sm">Messages</span>
                    </div>
                    <span className="font-medium">{period.messages.toLocaleString()}</span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-green-500" />
                      <span className="text-sm">Users</span>
                    </div>
                    <span className="font-medium">{period.users}</span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-orange-500" />
                      <span className="text-sm">Cost</span>
                    </div>
                    <span className="font-medium">${period.cost.toFixed(2)}</span>
                  </div>
                </div>

                {index < usageData.length - 1 && (
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <TrendingUp className="h-3 w-3 text-green-500" />
                    Growth trend positive
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Cost Analysis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Cost Analysis
          </CardTitle>
          <CardDescription>
            Detailed breakdown of AI usage costs
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-4">
              <h4 className="text-sm font-medium">Cost by Provider</h4>
              {providerUsage.map((provider) => (
                <div key={provider.name} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${provider.color}`} />
                    <span className="text-sm">{provider.name}</span>
                  </div>
                  <span className="text-sm font-medium">${provider.cost.toFixed(2)}</span>
                </div>
              ))}
            </div>
            
            <div className="space-y-4">
              <h4 className="text-sm font-medium">Usage Statistics</h4>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Avg. cost per message</span>
                  <span className="font-medium">
                    ${(metrics.totalCost / Math.max(metrics.totalMessages, 1)).toFixed(4)}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Avg. tokens per message</span>
                  <span className="font-medium">
                    {Math.round(metrics.totalTokens / Math.max(metrics.totalMessages, 1))}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Cost per 1K tokens</span>
                  <span className="font-medium">$0.002</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AnalyticsTab;
