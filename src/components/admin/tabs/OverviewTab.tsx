import React from "react";
import { motion } from "framer-motion";
import {
  Act<PERSON>,
  Bot,
  Clock,
  Database,
  DollarSign,
  Key,
  MessageSquare,
  UserPlus,
  Users,
  Zap,
  TrendingUp,
  Server,
  CheckCircle,
  Globe,
  AlertTriangle,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Session, SystemMetrics, User } from "@/types";

interface OverviewTabProps {
  systemMetrics: SystemMetrics | null;
  users: User[];
  sessions: Session[];
}

const OverviewTab: React.FC<OverviewTabProps> = ({
  systemMetrics,
  users,
  sessions,
}) => {
  // Calculate derived metrics
  const activeUsers = users.filter(user => {
    const lastActive = new Date(user.lastActive);
    const now = new Date();
    const hoursDiff = (now.getTime() - lastActive.getTime()) / (1000 * 60 * 60);
    return hoursDiff < 24; // Active in last 24 hours
  }).length;

  const activeSessions = sessions.filter(session => session.status === "active").length;
  const totalMessages = sessions.reduce((sum, session) => sum + session.messageCount, 0);
  const totalCost = sessions.reduce((sum, session) => sum + session.cost, 0);

  // Use real metrics if available, otherwise calculate from data
  const metrics = systemMetrics || {
    totalUsers: users.length,
    activeUsers,
    totalSessions: sessions.length,
    activeSessions,
    totalMessages,
    totalTokens: totalMessages * 150,
    totalCost,
    averageResponseTime: 1.2,
    errorRate: 0.02,
    uptime: 99.8,
    lastUpdated: new Date(),
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      {/* System Status Banner */}
        <Card className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-green-100 dark:bg-green-900">
                  <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <h3 className="font-semibold text-green-900 dark:text-green-100">
                    System Operational
                  </h3>
                  <p className="text-sm text-green-700 dark:text-green-300">
                    All services running normally • Uptime: {metrics.uptime}%
                  </p>
                </div>
              </div>
              <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                <Activity className="mr-1 h-3 w-3" />
                Live
              </Badge>
            </div>
          </CardContent>
        </Card>

      {/* Key Metrics Grid */}
      <motion.div variants={itemVariants} className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
       
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalUsers}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+{activeUsers}</span> active today
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Active Sessions
            </CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {systemMetrics?.activeSessions ||
                sessions.filter((s) => s.status === "active").length}
            </div>
            <p className="text-xs text-muted-foreground">
              {systemMetrics?.totalSessions || sessions.length} total
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Messages
            </CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {systemMetrics?.totalMessages?.toLocaleString() || "0"}
            </div>
            <p className="text-xs text-muted-foreground">
              {systemMetrics?.totalTokens?.toLocaleString() || "0"}{" "}
              tokens
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Cost
            </CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${systemMetrics?.totalCost?.toFixed(2) || "0.00"}
            </div>
            <p className="text-xs text-muted-foreground">
              Avg:{" "}
              {systemMetrics?.averageResponseTime?.toFixed(0) || "0"}ms
            </p>
          </CardContent>
        </Card>
            </div>

              {/* System Health */}
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="h-5 w-5" />
                      System Health
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Uptime</span>
                      <span className="text-sm text-green-600">
                        {systemMetrics?.uptime
                          ? `${(systemMetrics.uptime * 100).toFixed(1)}%`
                          : "99.9%"}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Error Rate</span>
                      <span className="text-sm text-red-600">
                        {systemMetrics?.errorRate
                          ? `${(systemMetrics.errorRate * 100).toFixed(2)}%`
                          : "0.1%"}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Response Time</span>
                      <span className="text-sm text-blue-600">
                        {systemMetrics?.averageResponseTime?.toFixed(0) ||
                          "120"}
                        ms
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">
                        Active Connections
                      </span>
                      <span className="text-sm text-purple-600">
                        {systemMetrics?.activeUsers || "1,247"}
                      </span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Clock className="h-5 w-5" />
                      Recent Activity
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <div className="h-2 w-2 bg-green-500 rounded-full" />
                        <div className="flex-1">
                          <p className="text-sm font-medium">
                            New user registered
                          </p>
                          <p className="text-xs text-muted-foreground">
                            2 minutes ago
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="h-2 w-2 bg-blue-500 rounded-full" />
                        <div className="flex-1">
                          <p className="text-sm font-medium">Agent deployed</p>
                          <p className="text-xs text-muted-foreground">
                            15 minutes ago
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="h-2 w-2 bg-yellow-500 rounded-full" />
                        <div className="flex-1">
                          <p className="text-sm font-medium">
                            Knowledge base updated
                          </p>
                          <p className="text-xs text-muted-foreground">
                            1 hour ago
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="h-2 w-2 bg-purple-500 rounded-full" />
                        <div className="flex-1">
                          <p className="text-sm font-medium">API key created</p>
                          <p className="text-xs text-muted-foreground">
                            2 hours ago
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5" />
                    Quick Actions
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
                    <Button
                      className="h-auto p-4 flex flex-col items-center gap-2"
                      variant="outline"
                    >
                      <Bot className="h-6 w-6" />
                      <span className="text-sm font-medium">Create Agent</span>
                    </Button>
                    <Button
                      className="h-auto p-4 flex flex-col items-center gap-2"
                      variant="outline"
                    >
                      <Database className="h-6 w-6" />
                      <span className="text-sm font-medium">
                        Add Knowledge Base
                      </span>
                    </Button>
                    <Button
                      className="h-auto p-4 flex flex-col items-center gap-2"
                      variant="outline"
                    >
                      <Key className="h-6 w-6" />
                      <span className="text-sm font-medium">Add API Key</span>
                    </Button>
                    <Button
                      className="h-auto p-4 flex flex-col items-center gap-2"
                      variant="outline"
                    >
                      <UserPlus className="h-6 w-6" />
                      <span className="text-sm font-medium">Invite User</span>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
            </motion.div>
  );
};

export default OverviewTab;
  