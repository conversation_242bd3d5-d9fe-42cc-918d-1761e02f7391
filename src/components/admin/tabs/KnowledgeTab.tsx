import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  PlusCircle,
  Trash2,
  Edit,
  Database,
  Search,
  RefreshCw,
  Upload,
  FileText,
  Globe,
  BookOpen,
  AlertTriangle,
  CheckCircle,
  Clock,
} from "lucide-react";
import { KnowledgeBase } from "@/types";

interface KnowledgeTabProps {
  knowledgeBases: KnowledgeBase[];
  onRefresh: () => void;
  onDeleteItem: (id: string, type: string) => void;
  searchTerm: string;
  onSearchChange: (value: string) => void;
  filterStatus: string;
  onFilterChange: (value: string) => void;
}

const KnowledgeTab: React.FC<KnowledgeTabProps> = ({
  knowledgeBases,
  onRefresh,
  onDeleteItem,
  searchTerm,
  onSearchChange,
  filterStatus,
  onFilterChange,
}) => {
  const [kbDialogOpen, setKbDialogOpen] = useState(false);
  const [selectedKB, setSelectedKB] = useState<KnowledgeBase | null>(null);

  const filteredKnowledgeBases = knowledgeBases.filter((kb) => {
    const matchesSearch = kb.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         kb.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === "all" || kb.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const handleCreateKB = () => {
    setSelectedKB(null);
    setKbDialogOpen(true);
  };

  const handleEditKB = (kb: KnowledgeBase) => {
    setSelectedKB(kb);
    setKbDialogOpen(true);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "indexing":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case "error":
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <Database className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "indexing":
        return "bg-yellow-100 text-yellow-800";
      case "error":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "documents":
        return <FileText className="h-4 w-4" />;
      case "qa":
        return <BookOpen className="h-4 w-4" />;
      case "web":
        return <Globe className="h-4 w-4" />;
      default:
        return <Database className="h-4 w-4" />;
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Knowledge Bases
          </CardTitle>
          <CardDescription>
            Manage document collections and knowledge sources for AI agents
          </CardDescription>
        </div>
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search knowledge bases..."
                value={searchTerm}
                onChange={(e) => onSearchChange(e.target.value)}
                className="pl-8 w-[200px]"
              />
            </div>
            <select
              value={filterStatus}
              onChange={(e) => onFilterChange(e.target.value)}
              className="px-3 py-2 border border-input bg-background rounded-md text-sm"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="indexing">Indexing</option>
              <option value="error">Error</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
          <Button variant="outline" size="sm" onClick={onRefresh}>
            <RefreshCw className="h-4 w-4" />
          </Button>
          <Button size="sm" onClick={handleCreateKB}>
            <PlusCircle className="h-4 w-4 mr-2" />
            Create Knowledge Base
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Documents</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Last Indexed</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredKnowledgeBases.map((kb) => (
              <TableRow key={kb.id}>
                <TableCell className="font-medium">
                  <div className="flex items-center gap-2">
                    <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                      {getTypeIcon(kb.type)}
                    </div>
                    <div>
                      <div className="font-medium">{kb.name}</div>
                      <div className="text-sm text-muted-foreground max-w-[200px] truncate">
                        {kb.description}
                      </div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant="outline">
                    {kb.type}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{kb.documentsCount}</span>
                    <span className="text-sm text-muted-foreground">docs</span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(kb.status)}
                    <Badge variant="outline" className={getStatusColor(kb.status)}>
                      {kb.status}
                    </Badge>
                  </div>
                  {kb.status === "indexing" && (
                    <div className="mt-1">
                      <Progress value={65} className="h-1 w-20" />
                    </div>
                  )}
                </TableCell>
                <TableCell>
                  {kb.lastIndexed 
                    ? new Date(kb.lastIndexed).toLocaleDateString()
                    : "Never"
                  }
                </TableCell>
                <TableCell>
                  {new Date(kb.createdAt).toLocaleDateString()}
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex items-center justify-end gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditKB(kb)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      disabled={kb.status === "indexing"}
                    >
                      <Upload className="h-4 w-4" />
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete Knowledge Base</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to delete "{kb.name}"? This will permanently remove all documents and cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            className="bg-destructive text-destructive-foreground"
                            onClick={() => onDeleteItem(kb.id, "knowledgeBase")}
                          >
                            Delete
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        {filteredKnowledgeBases.length === 0 && (
          <div className="text-center py-8">
            <Database className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No knowledge bases found</h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm || filterStatus !== "all"
                ? "No knowledge bases match your current filters."
                : "Get started by creating your first knowledge base."}
            </p>
            {(!searchTerm && filterStatus === "all") && (
              <Button onClick={handleCreateKB}>
                <PlusCircle className="h-4 w-4 mr-2" />
                Create Knowledge Base
              </Button>
            )}
          </div>
        )}
      </CardContent>

      {/* Knowledge Base Creation/Edit Dialog */}
      <Dialog open={kbDialogOpen} onOpenChange={setKbDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {selectedKB ? "Edit Knowledge Base" : "Create New Knowledge Base"}
            </DialogTitle>
            <DialogDescription>
              {selectedKB 
                ? "Update the knowledge base configuration and settings."
                : "Configure your new knowledge base for document storage and retrieval."
              }
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {/* TODO: Add knowledge base form fields */}
            <p className="text-sm text-muted-foreground">
              Knowledge base creation form will be implemented here.
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setKbDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => setKbDialogOpen(false)}>
              {selectedKB ? "Update Knowledge Base" : "Create Knowledge Base"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default KnowledgeTab;
