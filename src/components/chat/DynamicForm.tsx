import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { AlertCircle, CheckCircle2 } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface FormField {
  id: string;
  type: "text" | "textarea" | "select" | "checkbox" | "radio";
  label: string;
  placeholder?: string;
  required?: boolean;
  options?: { value: string; label: string }[];
  description?: string;
  defaultValue?: string | boolean;
  validation?: {
    pattern?: string;
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
  };
}

interface DynamicFormProps {
  formId?: string;
  title?: string;
  description?: string;
  fields?: FormField[];
  onSubmit?: (data: Record<string, any>) => void;
  onCancel?: () => void;
  isSubmitting?: boolean;
  submitButtonText?: string;
  cancelButtonText?: string;
}

const DynamicForm: React.FC<DynamicFormProps> = ({
  formId = "dynamic-form",
  title = "Please provide additional information",
  description = "The AI needs more details to assist you better.",
  fields = [
    {
      id: "sample-field",
      type: "text",
      label: "Sample Field",
      placeholder: "Enter value",
      required: true,
    },
  ],
  onSubmit = () => {},
  onCancel = () => {},
  isSubmitting = false,
  submitButtonText = "Submit",
  cancelButtonText = "Cancel",
}) => {
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isValid, setIsValid] = useState<boolean>(false);
  const [submitted, setSubmitted] = useState<boolean>(false);

  // Initialize form data with default values
  useEffect(() => {
    const initialData: Record<string, any> = {};
    fields.forEach((field) => {
      if (field.defaultValue !== undefined) {
        initialData[field.id] = field.defaultValue;
      } else if (field.type === "checkbox") {
        initialData[field.id] = false;
      } else {
        initialData[field.id] = "";
      }
    });
    setFormData(initialData);
  }, [fields]);

  // Validate form when data changes
  useEffect(() => {
    validateForm();
  }, [formData]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    let formIsValid = true;

    fields.forEach((field) => {
      const value = formData[field.id];

      // Check required fields
      if (
        field.required &&
        (value === "" || value === undefined || value === null)
      ) {
        newErrors[field.id] = "This field is required";
        formIsValid = false;
        return;
      }

      // Skip validation for empty optional fields
      if (value === "" && !field.required) {
        return;
      }

      // Validate based on field type and validation rules
      if (field.validation) {
        if (field.type === "text" || field.type === "textarea") {
          const stringValue = String(value);

          if (
            field.validation.pattern &&
            !new RegExp(field.validation.pattern).test(stringValue)
          ) {
            newErrors[field.id] = "Invalid format";
            formIsValid = false;
          }

          if (
            field.validation.minLength &&
            stringValue.length < field.validation.minLength
          ) {
            newErrors[field.id] =
              `Minimum length is ${field.validation.minLength} characters`;
            formIsValid = false;
          }

          if (
            field.validation.maxLength &&
            stringValue.length > field.validation.maxLength
          ) {
            newErrors[field.id] =
              `Maximum length is ${field.validation.maxLength} characters`;
            formIsValid = false;
          }
        }

        if (field.type === "text" && !isNaN(Number(value))) {
          const numValue = Number(value);

          if (
            field.validation.min !== undefined &&
            numValue < field.validation.min
          ) {
            newErrors[field.id] = `Minimum value is ${field.validation.min}`;
            formIsValid = false;
          }

          if (
            field.validation.max !== undefined &&
            numValue > field.validation.max
          ) {
            newErrors[field.id] = `Maximum value is ${field.validation.max}`;
            formIsValid = false;
          }
        }
      }
    });

    setErrors(newErrors);
    setIsValid(formIsValid);
    return formIsValid;
  };

  const handleChange = (id: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [id]: value,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitted(true);

    if (validateForm()) {
      onSubmit(formData);
    }
  };

  const renderField = (field: FormField) => {
    const { id, type, label, placeholder, required, options, description } =
      field;
    const error = errors[id];
    const hasError = submitted && error;

    switch (type) {
      case "text":
        return (
          <div className="space-y-2" key={id}>
            <Label htmlFor={id} className="flex items-center">
              {label} {required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            {description && (
              <p className="text-sm text-muted-foreground">{description}</p>
            )}
            <Input
              id={id}
              value={formData[id] || ""}
              onChange={(e) => handleChange(id, e.target.value)}
              placeholder={placeholder}
              className={hasError ? "border-red-500" : ""}
              disabled={isSubmitting}
            />
            {hasError && <p className="text-sm text-red-500">{error}</p>}
          </div>
        );

      case "textarea":
        return (
          <div className="space-y-2" key={id}>
            <Label htmlFor={id} className="flex items-center">
              {label} {required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            {description && (
              <p className="text-sm text-muted-foreground">{description}</p>
            )}
            <Textarea
              id={id}
              value={formData[id] || ""}
              onChange={(e) => handleChange(id, e.target.value)}
              placeholder={placeholder}
              className={hasError ? "border-red-500" : ""}
              disabled={isSubmitting}
            />
            {hasError && <p className="text-sm text-red-500">{error}</p>}
          </div>
        );

      case "select":
        return (
          <div className="space-y-2" key={id}>
            <Label htmlFor={id} className="flex items-center">
              {label} {required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            {description && (
              <p className="text-sm text-muted-foreground">{description}</p>
            )}
            <Select
              value={formData[id] || ""}
              onValueChange={(value) => handleChange(id, value)}
              disabled={isSubmitting}
            >
              <SelectTrigger className={hasError ? "border-red-500" : ""}>
                <SelectValue placeholder={placeholder || "Select an option"} />
              </SelectTrigger>
              <SelectContent>
                {options?.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {hasError && <p className="text-sm text-red-500">{error}</p>}
          </div>
        );

      case "checkbox":
        return (
          <div className="space-y-2" key={id}>
            <div className="flex items-center space-x-2">
              <Checkbox
                id={id}
                checked={!!formData[id]}
                onCheckedChange={(checked) => handleChange(id, checked)}
                disabled={isSubmitting}
              />
              <Label htmlFor={id} className="flex items-center">
                {label}{" "}
                {required && <span className="text-red-500 ml-1">*</span>}
              </Label>
            </div>
            {description && (
              <p className="text-sm text-muted-foreground">{description}</p>
            )}
            {hasError && <p className="text-sm text-red-500">{error}</p>}
          </div>
        );

      case "radio":
        return (
          <div className="space-y-2" key={id}>
            <Label className="flex items-center">
              {label} {required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            {description && (
              <p className="text-sm text-muted-foreground">{description}</p>
            )}
            <RadioGroup
              value={formData[id] || ""}
              onValueChange={(value) => handleChange(id, value)}
              className="flex flex-col space-y-1"
              disabled={isSubmitting}
            >
              {options?.map((option) => (
                <div className="flex items-center space-x-2" key={option.value}>
                  <RadioGroupItem
                    value={option.value}
                    id={`${id}-${option.value}`}
                  />
                  <Label htmlFor={`${id}-${option.value}`}>
                    {option.label}
                  </Label>
                </div>
              ))}
            </RadioGroup>
            {hasError && <p className="text-sm text-red-500">{error}</p>}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Card className="w-full bg-white shadow-md">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <form id={formId} onSubmit={handleSubmit} className="space-y-4">
          {submitted && !isValid && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Please fix the errors in the form before submitting.
              </AlertDescription>
            </Alert>
          )}

          {submitted && isValid && isSubmitting && (
            <Alert className="mb-4 bg-blue-50 border-blue-200">
              <div className="flex items-center">
                <div className="animate-spin mr-2 h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                <AlertDescription>
                  Processing your submission...
                </AlertDescription>
              </div>
            </Alert>
          )}

          {fields.map(renderField)}
        </form>
      </CardContent>
      <CardFooter className="flex justify-end space-x-2">
        <Button variant="outline" onClick={onCancel} disabled={isSubmitting}>
          {cancelButtonText}
        </Button>
        <Button type="submit" form={formId} disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <div className="animate-spin mr-2 h-4 w-4 border-2 border-current border-t-transparent rounded-full"></div>
              Processing...
            </>
          ) : (
            submitButtonText
          )}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default DynamicForm;
