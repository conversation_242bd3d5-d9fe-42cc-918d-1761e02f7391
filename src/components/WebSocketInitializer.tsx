import { useEffect } from "react";
import WebSocketService from "@/services/websocket";
import AuthService from "@/services/auth";
import RoutingRulesService from "@/services/routingRules";
import APIProviderService from "@/services/apiProvider";
import UAUICoreService from "@/services/uauiCore";

const WebSocketInitializer: React.FC = () => {
  useEffect(() => {
    const initializeServices = async () => {
      try {
        // Initialize authentication state
        const authState = AuthService.getAuthState();

        if (authState.isAuthenticated) {
          // Initialize WebSocket connection
          WebSocketService.connect();

          // Load routing rules
          await RoutingRulesService.loadRules();

          // Initialize UAUI Core Service
          const healthCheck = await UAUICoreService.healthCheck();
          console.log("UAUI Core Health:", healthCheck);

          // Set up UAUI event listeners
          UAUICoreService.on("processing_start", (data) => {
            console.log("UAUI processing started:", data);
          });

          UAUICoreService.on("processing_complete", (data) => {
            console.log("UAUI processing completed:", data);
          });

          UAUICoreService.on("processing_error", (data) => {
            console.error("UAUI processing error:", data);
          });

          // Initialize API providers with available keys
          // This would typically load encrypted API keys from the backend
          console.log("Services initialized successfully");
        }
      } catch (error) {
        console.error("Failed to initialize services:", error);
      }
    };

    initializeServices();

    // Cleanup on unmount
    return () => {
      WebSocketService.disconnect();
    };
  }, []);

  // This component doesn't render anything visible
  return null;
};

export default WebSocketInitializer;
