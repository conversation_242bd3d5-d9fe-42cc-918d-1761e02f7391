import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Save, RefreshCw, CheckCircle } from "lucide-react";
import UserPreferencesService from "@/services/userPreferences";
import SystemPromptsService from "@/services/systemPrompts";
import { UserPreferences, SystemPrompt, Agent } from "@/types";

interface UserSettingsPanelProps {
  onPreferencesUpdate?: (preferences: UserPreferences) => void;
}

const UserSettingsPanel: React.FC<UserSettingsPanelProps> = ({
  onPreferencesUpdate,
}) => {
  const [preferences, setPreferences] = useState<UserPreferences | null>(null);
  const [systemPrompts, setSystemPrompts] = useState<SystemPrompt[]>([]);
  const [agents, setAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [originalPreferences, setOriginalPreferences] =
    useState<UserPreferences | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [userPrefs, prompts, availableAgents] = await Promise.all([
        UserPreferencesService.getUserPreferences(),
        SystemPromptsService.getSystemPrompts().catch(() => []), // Non-admin users can't access system prompts
        fetch("/api/agents/public")
          .then((res) => (res.ok ? res.json() : []))
          .catch(() => []), // Get public agents
      ]);

      setPreferences(userPrefs);
      setOriginalPreferences(userPrefs);
      setSystemPrompts(prompts);
      setAgents(availableAgents.agents || []);
      setHasChanges(false);
    } catch (error: any) {
      console.error("Error loading user settings:", error);
      setError(error.message || "Failed to load settings");
    } finally {
      setLoading(false);
    }
  };

  const handlePreferenceChange = <K extends keyof UserPreferences>(
    key: K,
    value: UserPreferences[K],
  ) => {
    if (!preferences) return;

    const updatedPreferences = {
      ...preferences,
      [key]: value,
    };

    setPreferences(updatedPreferences);
    setHasChanges(
      JSON.stringify(updatedPreferences) !==
        JSON.stringify(originalPreferences),
    );
    setSuccess(false);
  };

  const handleSave = async () => {
    if (!preferences || !hasChanges) return;

    try {
      setSaving(true);
      setError(null);

      const updatedPreferences =
        await UserPreferencesService.updateUserPreferences({
          defaultProvider: preferences.defaultProvider,
          defaultAgent: preferences.defaultAgent,
          enableThinkingIndicators: preferences.enableThinkingIndicators,
          enableCodeHighlighting: preferences.enableCodeHighlighting,
          enableNotifications: preferences.enableNotifications,
          enableHITL: preferences.enableHITL,
          theme: preferences.theme,
          language: preferences.language,
          systemPromptId: preferences.systemPromptId,
          favoriteAgents: preferences.favoriteAgents,
        });

      setPreferences(updatedPreferences);
      setOriginalPreferences(updatedPreferences);
      setHasChanges(false);
      setSuccess(true);

      // Notify parent component
      onPreferencesUpdate?.(updatedPreferences);

      // Hide success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000);
    } catch (error: any) {
      console.error("Error saving preferences:", error);
      setError(error.message || "Failed to save preferences");
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    if (originalPreferences) {
      setPreferences(originalPreferences);
      setHasChanges(false);
      setSuccess(false);
      setError(null);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading settings...</p>
        </div>
      </div>
    );
  }

  if (!preferences) {
    return (
      <div className="p-8">
        <Alert variant="destructive">
          <AlertDescription>
            Failed to load user preferences. Please try refreshing the page.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6 max-w-4xl mx-auto">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">User Settings</h2>
          <p className="text-muted-foreground">
            Customize your AI chat experience and preferences
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={loadData} disabled={loading}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="bg-green-50 border-green-200">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            Settings saved successfully!
          </AlertDescription>
        </Alert>
      )}

      <div className="grid gap-6">
        {/* AI Provider Preferences */}
        <Card>
          <CardHeader>
            <CardTitle>AI Provider Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="default-provider">Default AI Provider</Label>
              <Select
                value={preferences.defaultProvider}
                onValueChange={(value: any) =>
                  handlePreferenceChange("defaultProvider", value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select default provider" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="openai">OpenAI (GPT-4)</SelectItem>
                  <SelectItem value="claude">Claude (Anthropic)</SelectItem>
                  <SelectItem value="gemini">Gemini (Google)</SelectItem>
                  <SelectItem value="mistral">Mistral AI</SelectItem>
                  <SelectItem value="groq">Groq</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-sm text-muted-foreground">
                This provider will be used by default for new conversations.
                Routing rules may override this selection.
              </p>
            </div>

            {agents.length > 0 && (
              <div className="space-y-2">
                <Label htmlFor="default-agent">Default Agent</Label>
                <Select
                  value={preferences.defaultAgent || ""}
                  onValueChange={(value) =>
                    handlePreferenceChange("defaultAgent", value || undefined)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select default agent (optional)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">
                      None (Use Provider Default)
                    </SelectItem>
                    {agents
                      .filter((agent) => agent.isActive && agent.isPublic)
                      .map((agent) => (
                        <SelectItem key={agent.id} value={agent.id}>
                          {agent.name} - {agent.description}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">
                  Choose a default agent for new conversations. Agents have
                  specialized knowledge and capabilities.
                </p>
              </div>
            )}

            {systemPrompts.length > 0 && (
              <div className="space-y-2">
                <Label htmlFor="system-prompt">System Prompt</Label>
                <Select
                  value={preferences.systemPromptId || ""}
                  onValueChange={(value) =>
                    handlePreferenceChange("systemPromptId", value || undefined)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select system prompt (optional)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">None (Default)</SelectItem>
                    {systemPrompts
                      .filter((prompt) => prompt.isActive)
                      .map((prompt) => (
                        <SelectItem key={prompt.id} value={prompt.id}>
                          {prompt.name} ({prompt.context})
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">
                  Choose a system prompt to define the AI's behavior and
                  personality.
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Interface Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Interface Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Show Thinking Indicators</Label>
                <p className="text-sm text-muted-foreground">
                  Display animated indicators when the AI is processing your
                  request
                </p>
              </div>
              <Switch
                checked={preferences.enableThinkingIndicators}
                onCheckedChange={(checked) =>
                  handlePreferenceChange("enableThinkingIndicators", checked)
                }
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Enable Code Highlighting</Label>
                <p className="text-sm text-muted-foreground">
                  Syntax highlighting for code blocks in AI responses
                </p>
              </div>
              <Switch
                checked={preferences.enableCodeHighlighting}
                onCheckedChange={(checked) =>
                  handlePreferenceChange("enableCodeHighlighting", checked)
                }
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Enable Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Receive browser notifications for important updates
                </p>
              </div>
              <Switch
                checked={preferences.enableNotifications}
                onCheckedChange={(checked) =>
                  handlePreferenceChange("enableNotifications", checked)
                }
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Enable Human-in-the-Loop (HITL)</Label>
                <p className="text-sm text-muted-foreground">
                  Allow AI to request human assistance for complex tasks
                </p>
              </div>
              <Switch
                checked={preferences.enableHITL || false}
                onCheckedChange={(checked) =>
                  handlePreferenceChange("enableHITL", checked)
                }
              />
            </div>
          </CardContent>
        </Card>

        {/* Appearance Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Appearance</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="theme">Theme</Label>
              <Select
                value={preferences.theme}
                onValueChange={(value: any) =>
                  handlePreferenceChange("theme", value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select theme" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="light">Light</SelectItem>
                  <SelectItem value="dark">Dark</SelectItem>
                  <SelectItem value="system">System</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-sm text-muted-foreground">
                Choose your preferred color scheme
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="language">Language</Label>
              <Select
                value={preferences.language}
                onValueChange={(value) =>
                  handlePreferenceChange("language", value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select language" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="en">English</SelectItem>
                  <SelectItem value="es">Español</SelectItem>
                  <SelectItem value="fr">Français</SelectItem>
                  <SelectItem value="de">Deutsch</SelectItem>
                  <SelectItem value="it">Italiano</SelectItem>
                  <SelectItem value="pt">Português</SelectItem>
                  <SelectItem value="zh">中文</SelectItem>
                  <SelectItem value="ja">日本語</SelectItem>
                  <SelectItem value="ko">한국어</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-sm text-muted-foreground">
                Interface language preference
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Save/Reset Actions */}
      <div className="flex items-center justify-end gap-3 pt-6 border-t">
        <Button
          variant="outline"
          onClick={handleReset}
          disabled={!hasChanges || saving}
        >
          Reset Changes
        </Button>
        <Button
          onClick={handleSave}
          disabled={!hasChanges || saving}
          className="min-w-[120px]"
        >
          {saving ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              Saving...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default UserSettingsPanel;
