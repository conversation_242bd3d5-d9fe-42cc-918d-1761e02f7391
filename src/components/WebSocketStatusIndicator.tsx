import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Wifi, WifiOff, <PERSON>ader2, Al<PERSON><PERSON>riangle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import {
  Toolt<PERSON>,
  Toolt<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>Provider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import WebSocketService from "@/services/websocket";
import { ConnectionStatus } from "@/types";

interface WebSocketStatusIndicatorProps {
  showText?: boolean;
  size?: "sm" | "md" | "lg";
  variant?: "badge" | "icon" | "full";
  onReconnect?: () => void;
}

const WebSocketStatusIndicator: React.FC<WebSocketStatusIndicatorProps> = ({
  showText = true,
  size = "md",
  variant = "full",
  onReconnect,
}) => {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    status: "disconnected",
    reconnectAttempts: 0,
  });

  useEffect(() => {
    // Get initial status
    setConnectionStatus(WebSocketService.getConnectionStatus());

    // Subscribe to status changes
    const unsubscribe = WebSocketService.onConnectionStatusChange((status) => {
      setConnectionStatus(status);
    });

    return unsubscribe;
  }, []);

  const getStatusConfig = () => {
    switch (connectionStatus.status) {
      case "connected":
        return {
          color: "bg-green-500",
          textColor: "text-green-700",
          bgColor: "bg-green-50",
          borderColor: "border-green-200",
          text: "Connected",
          icon: Wifi,
          badgeVariant: "default" as const,
        };
      case "connecting":
        return {
          color: "bg-yellow-500",
          textColor: "text-yellow-700",
          bgColor: "bg-yellow-50",
          borderColor: "border-yellow-200",
          text: "Connecting...",
          icon: Loader2,
          badgeVariant: "secondary" as const,
        };
      case "reconnecting":
        return {
          color: "bg-orange-500",
          textColor: "text-orange-700",
          bgColor: "bg-orange-50",
          borderColor: "border-orange-200",
          text: `Reconnecting... (${connectionStatus.reconnectAttempts})`,
          icon: Loader2,
          badgeVariant: "secondary" as const,
        };
      case "disconnected":
      default:
        return {
          color: "bg-red-500",
          textColor: "text-red-700",
          bgColor: "bg-red-50",
          borderColor: "border-red-200",
          text: "Disconnected",
          icon: WifiOff,
          badgeVariant: "destructive" as const,
        };
    }
  };

  const statusConfig = getStatusConfig();
  const StatusIcon = statusConfig.icon;

  const getIconSize = () => {
    switch (size) {
      case "sm":
        return "h-3 w-3";
      case "lg":
        return "h-5 w-5";
      case "md":
      default:
        return "h-4 w-4";
    }
  };

  const getDotSize = () => {
    switch (size) {
      case "sm":
        return "h-2 w-2";
      case "lg":
        return "h-3 w-3";
      case "md":
      default:
        return "h-2.5 w-2.5";
    }
  };

  const handleReconnect = () => {
    if (onReconnect) {
      onReconnect();
    } else {
      WebSocketService.refreshConnection();
    }
  };

  const getTooltipContent = () => {
    const lastConnected = connectionStatus.lastConnected
      ? new Date(connectionStatus.lastConnected).toLocaleString()
      : "Never";

    return (
      <div className="space-y-1">
        <div className="font-medium">WebSocket Status</div>
        <div className="text-sm">
          Status:{" "}
          <span className={statusConfig.textColor}>{statusConfig.text}</span>
        </div>
        <div className="text-sm">Last Connected: {lastConnected}</div>
        {connectionStatus.reconnectAttempts > 0 && (
          <div className="text-sm">
            Reconnect Attempts: {connectionStatus.reconnectAttempts}
          </div>
        )}
        {connectionStatus.status === "disconnected" && (
          <Button
            size="sm"
            variant="outline"
            onClick={handleReconnect}
            className="mt-2 w-full"
          >
            Reconnect
          </Button>
        )}
      </div>
    );
  };

  if (variant === "badge") {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge variant={statusConfig.badgeVariant} className="cursor-help">
              <div
                className={`${getDotSize()} rounded-full ${statusConfig.color} mr-1`}
              />
              {showText && statusConfig.text}
            </Badge>
          </TooltipTrigger>
          <TooltipContent>{getTooltipContent()}</TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  if (variant === "icon") {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="cursor-help">
              {connectionStatus.status === "connecting" ||
              connectionStatus.status === "reconnecting" ? (
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                >
                  <StatusIcon
                    className={`${getIconSize()} ${statusConfig.textColor}`}
                  />
                </motion.div>
              ) : (
                <StatusIcon
                  className={`${getIconSize()} ${statusConfig.textColor}`}
                />
              )}
            </div>
          </TooltipTrigger>
          <TooltipContent>{getTooltipContent()}</TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // Full variant
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            className={`flex items-center gap-2 px-3 py-1 rounded-md border cursor-help transition-colors ${
              statusConfig.bgColor
            } ${statusConfig.borderColor}`}
          >
            <div
              className={`${getDotSize()} rounded-full ${statusConfig.color}`}
            />
            {connectionStatus.status === "connecting" ||
            connectionStatus.status === "reconnecting" ? (
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              >
                <StatusIcon
                  className={`${getIconSize()} ${statusConfig.textColor}`}
                />
              </motion.div>
            ) : (
              <StatusIcon
                className={`${getIconSize()} ${statusConfig.textColor}`}
              />
            )}
            {showText && (
              <span className={`text-sm font-medium ${statusConfig.textColor}`}>
                {statusConfig.text}
              </span>
            )}
            {connectionStatus.status === "disconnected" && (
              <Button
                size="sm"
                variant="ghost"
                onClick={handleReconnect}
                className="ml-2 h-6 px-2 text-xs"
              >
                Retry
              </Button>
            )}
          </div>
        </TooltipTrigger>
        <TooltipContent>{getTooltipContent()}</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default WebSocketStatusIndicator;
