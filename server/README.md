# UAUI Backend Server

Production-ready backend server for the Universal AI UI (UAUI) system with APIX protocol integration.

## Features

- **Complete REST API** with authentication, authorization, and rate limiting
- **Real-time WebSocket communication** using Socket.IO with APIX protocol
- **Multi-provider AI integration** (OpenAI, Claude, Gemini, Mistral, Groq)
- **SQLite database** with full schema and migrations
- **JWT authentication** with token refresh
- **File upload support** with validation and storage
- **Email notifications** via SMTP
- **Comprehensive audit logging** for all user actions
- **Performance metrics** and monitoring
- **Production-grade security** with encryption, rate limiting, and validation

## Quick Start

1. **Install dependencies:**
   ```bash
   cd server
   npm install
   ```

2. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start the server:**
   ```bash
   npm run dev
   ```

## Environment Configuration

Copy `.env.example` to `.env` and configure:

- `JWT_SECRET`: Strong secret for JWT token signing
- `ENCRYPTION_KEY`: 32-character key for API key encryption
- `SMTP_*`: Email server configuration for notifications
- `FRONTEND_URL`: Your frontend URL for CORS

## Database Schema

The server automatically creates these tables:

- `users` - User accounts with authentication
- `api_keys` - Encrypted AI provider API keys
- `conversations` - Chat conversation metadata
- `messages` - Individual chat messages
- `routing_rules` - AI provider routing configuration
- `system_prompts` - Custom system prompts
- `user_preferences` - User settings and preferences
- `audit_logs` - Complete activity audit trail

## API Endpoints

### Authentication
- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `POST /auth/refresh` - Token refresh
- `POST /auth/logout` - User logout

### API Key Management (Admin only)
- `GET /api/keys` - List all API keys
- `POST /api/keys` - Add new API key
- `PUT /api/keys/:id` - Update API key
- `DELETE /api/keys/:id` - Delete API key

### Conversations
- `GET /api/conversations` - List user conversations
- `POST /api/conversations` - Create new conversation

### File Upload
- `POST /api/upload` - Upload files with validation

### Metrics (Admin only)
- `GET /api/metrics` - System performance metrics

## WebSocket Events (APIX Protocol)

### Client to Server
- `apix:send_message` - Send chat message
- `apix:submit_form` - Submit dynamic form
- `apix:cancel` - Cancel ongoing request

### Server to Client
- `apix:thinking` - AI is processing
- `apix:stream` - Streaming response chunk
- `apix:message` - Complete message
- `apix:form_request` - Request form input
- `apix:tool_call` - Tool execution
- `apix:complete` - Request completed
- `apix:error` - Error occurred

## AI Provider Integration

Supported providers with full streaming support:

- **OpenAI** - GPT-4, GPT-3.5-turbo
- **Claude** - Claude-3 Sonnet, Haiku, Opus
- **Gemini** - Gemini Pro with streaming
- **Mistral** - Mistral Medium, Large
- **Groq** - Mixtral, Llama models

## Security Features

- **JWT Authentication** with automatic refresh
- **API Key Encryption** using AES-256-CBC
- **Rate Limiting** to prevent abuse
- **Input Validation** on all endpoints
- **CORS Protection** with configurable origins
- **Helmet.js** for security headers
- **Audit Logging** for compliance

## Production Deployment

1. **Set NODE_ENV=production**
2. **Configure strong secrets** in environment variables
3. **Set up HTTPS** with reverse proxy (nginx/Apache)
4. **Configure email server** for notifications
5. **Set up log rotation** for audit logs
6. **Monitor performance** using `/api/metrics`

## File Structure

```
server/
├── index.js          # Main server file
├── package.json      # Dependencies
├── .env.example      # Environment template
├── README.md         # This file
├── logs/            # Application logs
├── uploads/         # File uploads
└── database.sqlite  # SQLite database
```

## Logging

The server uses Winston for comprehensive logging:
- `logs/error.log` - Error messages only
- `logs/combined.log` - All log messages
- Console output in development

## Support

This is a production-ready implementation with:
- ✅ Real database persistence
- ✅ Actual AI provider integrations
- ✅ Complete authentication system
- ✅ File upload functionality
- ✅ Email notifications
- ✅ Comprehensive audit logging
- ✅ Performance monitoring
- ✅ Production security measures

No mock data or placeholders - everything is fully functional.
