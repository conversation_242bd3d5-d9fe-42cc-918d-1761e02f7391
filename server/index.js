import express from "express";
import { createServer } from "http";
import { Server } from "socket.io";
import cors from "cors";
import helmet from "helmet";
import rateLimit from "express-rate-limit";
import jwt from "jsonwebtoken";
import bcrypt from "bcryptjs";
import { body, validationResult } from "express-validator";
import winston from "winston";
import knex from "knex";
import crypto from "crypto";
import { v4 as uuidv4 } from "uuid";
import multer from "multer";
import nodemailer from "nodemailer";
import fs from "fs";
import path from "path";
import Redis from "ioredis";

// Initialize logger
const logger = winston.createLogger({
  level: "info",
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json(),
  ),
  transports: [
    new winston.transports.File({ filename: "logs/error.log", level: "error" }),
    new winston.transports.File({ filename: "logs/combined.log" }),
    new winston.transports.Console({
      format: winston.format.simple(),
    }),
  ],
});

// Create logs directory
if (!fs.existsSync("logs")) {
  fs.mkdirSync("logs");
}

// Redis configuration
const redis = new Redis({
  host: process.env.REDIS_HOST || "localhost",
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD,
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
});

// Database configuration
const db = knex({
  client: "sqlite3",
  connection: {
    filename: "./database.sqlite",
  },
  useNullAsDefault: true,
});

// Initialize database tables
async function initializeDatabase() {
  try {
    // Users table
    if (!(await db.schema.hasTable("users"))) {
      await db.schema.createTable("users", (table) => {
        table.string("id").primary();
        table.string("email").unique().notNullable();
        table.string("name").notNullable();
        table.string("password_hash").notNullable();
        table
          .enum("role", ["admin", "user", "agent_builder", "knowledge_manager"])
          .defaultTo("user");
        table.json("permissions").defaultTo(JSON.stringify([]));
        table.string("organization_id");
        table.timestamp("created_at").defaultTo(db.fn.now());
        table.timestamp("last_active").defaultTo(db.fn.now());
      });
    }

    // Organizations table
    if (!(await db.schema.hasTable("organizations"))) {
      await db.schema.createTable("organizations", (table) => {
        table.string("id").primary();
        table.string("name").notNullable();
        table.string("slug").unique().notNullable();
        table.json("settings").defaultTo(JSON.stringify({}));
        table.timestamp("created_at").defaultTo(db.fn.now());
        table.timestamp("updated_at").defaultTo(db.fn.now());
      });
    }

    // API Keys table
    if (!(await db.schema.hasTable("api_keys"))) {
      await db.schema.createTable("api_keys", (table) => {
        table.string("id").primary();
        table
          .enum("provider", ["openai", "claude", "gemini", "mistral", "groq"])
          .notNullable();
        table.string("name").notNullable();
        table.text("encrypted_key").notNullable();
        table.enum("status", ["active", "inactive"]).defaultTo("active");
        table.timestamp("last_used");
        table.timestamp("created_at").defaultTo(db.fn.now());
        table.timestamp("updated_at").defaultTo(db.fn.now());
      });
    }

    // Agents table
    if (!(await db.schema.hasTable("agents"))) {
      await db.schema.createTable("agents", (table) => {
        table.string("id").primary();
        table.string("name").notNullable();
        table.text("description");
        table.string("avatar");
        table.text("system_prompt").notNullable();
        table.string("model").defaultTo("gpt-4");
        table.float("temperature").defaultTo(0.7);
        table.integer("max_tokens").defaultTo(2000);
        table.json("tools").defaultTo(JSON.stringify([]));
        table.json("knowledge_bases").defaultTo(JSON.stringify([]));
        table.boolean("is_public").defaultTo(false);
        table.boolean("is_active").defaultTo(true);
        table.string("created_by").notNullable();
        table.string("organization_id");
        table.json("metadata").defaultTo(JSON.stringify({}));
        table.timestamp("created_at").defaultTo(db.fn.now());
        table.timestamp("updated_at").defaultTo(db.fn.now());
        table.foreign("created_by").references("users.id");
      });
    }

    // Tools table
    if (!(await db.schema.hasTable("tools"))) {
      await db.schema.createTable("tools", (table) => {
        table.string("id").primary();
        table.string("name").notNullable();
        table.text("description");
        table
          .enum("type", ["function", "api", "webhook", "database", "custom"])
          .notNullable();
        table.json("schema").notNullable();
        table.json("configuration").notNullable();
        table.boolean("is_active").defaultTo(true);
        table.string("created_by").notNullable();
        table.string("organization_id");
        table.timestamp("created_at").defaultTo(db.fn.now());
        table.timestamp("updated_at").defaultTo(db.fn.now());
        table.foreign("created_by").references("users.id");
      });
    }

    // Knowledge Bases table
    if (!(await db.schema.hasTable("knowledge_bases"))) {
      await db.schema.createTable("knowledge_bases", (table) => {
        table.string("id").primary();
        table.string("name").notNullable();
        table.text("description");
        table
          .enum("type", ["documents", "qa", "structured", "web"])
          .notNullable();
        table
          .enum("status", ["active", "indexing", "error", "inactive"])
          .defaultTo("inactive");
        table.integer("documents_count").defaultTo(0);
        table.timestamp("last_indexed");
        table.json("settings").defaultTo(JSON.stringify({}));
        table.string("created_by").notNullable();
        table.string("organization_id");
        table.timestamp("created_at").defaultTo(db.fn.now());
        table.timestamp("updated_at").defaultTo(db.fn.now());
        table.foreign("created_by").references("users.id");
      });
    }

    // Documents table
    if (!(await db.schema.hasTable("documents"))) {
      await db.schema.createTable("documents", (table) => {
        table.string("id").primary();
        table.string("knowledge_base_id").notNullable();
        table.string("name").notNullable();
        table.string("type").notNullable();
        table.integer("size");
        table.text("content");
        table.json("metadata").defaultTo(JSON.stringify({}));
        table
          .enum("status", ["processing", "indexed", "error"])
          .defaultTo("processing");
        table.string("uploaded_by").notNullable();
        table.timestamp("created_at").defaultTo(db.fn.now());
        table.timestamp("updated_at").defaultTo(db.fn.now());
        table.foreign("knowledge_base_id").references("knowledge_bases.id");
        table.foreign("uploaded_by").references("users.id");
      });
    }

    // Conversations table
    if (!(await db.schema.hasTable("conversations"))) {
      await db.schema.createTable("conversations", (table) => {
        table.string("id").primary();
        table.string("user_id").notNullable();
        table.string("agent_id");
        table.string("title").notNullable();
        table
          .enum("status", ["active", "completed", "archived"])
          .defaultTo("active");
        table.integer("message_count").defaultTo(0);
        table.integer("tokens_used").defaultTo(0);
        table.float("cost").defaultTo(0);
        table.string("provider");
        table.json("metadata").defaultTo(JSON.stringify({}));
        table.timestamp("created_at").defaultTo(db.fn.now());
        table.timestamp("updated_at").defaultTo(db.fn.now());
        table.timestamp("last_activity").defaultTo(db.fn.now());
        table.foreign("user_id").references("users.id");
        table.foreign("agent_id").references("agents.id");
      });
    }

    // Messages table
    if (!(await db.schema.hasTable("messages"))) {
      await db.schema.createTable("messages", (table) => {
        table.string("id").primary();
        table.string("conversation_id").notNullable();
        table.enum("role", ["user", "assistant", "system"]).notNullable();
        table.text("content").notNullable();
        table.timestamp("timestamp").defaultTo(db.fn.now());
        table
          .enum("status", ["complete", "streaming", "thinking", "error"])
          .defaultTo("complete");
        table.string("provider");
        table.json("tool_calls");
        table.foreign("conversation_id").references("conversations.id");
      });
    }

    // HITL Requests table
    if (!(await db.schema.hasTable("hitl_requests"))) {
      await db.schema.createTable("hitl_requests", (table) => {
        table.string("id").primary();
        table.string("conversation_id").notNullable();
        table.string("message_id").notNullable();
        table
          .enum("type", ["approval", "input", "clarification", "escalation"])
          .notNullable();
        table.string("title").notNullable();
        table.text("description");
        table.json("context").defaultTo(JSON.stringify({}));
        table
          .enum("status", ["pending", "approved", "rejected", "completed"])
          .defaultTo("pending");
        table.string("assigned_to");
        table.text("response");
        table.json("metadata").defaultTo(JSON.stringify({}));
        table.timestamp("created_at").defaultTo(db.fn.now());
        table.timestamp("updated_at").defaultTo(db.fn.now());
        table.timestamp("resolved_at");
        table.foreign("conversation_id").references("conversations.id");
        table.foreign("message_id").references("messages.id");
        table.foreign("assigned_to").references("users.id");
      });
    }

    // Routing Rules table
    if (!(await db.schema.hasTable("routing_rules"))) {
      await db.schema.createTable("routing_rules", (table) => {
        table.string("id").primary();
        table.string("name").notNullable();
        table.integer("priority").notNullable();
        table.text("condition").notNullable();
        table
          .enum("provider", ["openai", "claude", "gemini", "mistral", "groq"])
          .notNullable();
        table.boolean("is_active").defaultTo(true);
        table.timestamp("created_at").defaultTo(db.fn.now());
        table.timestamp("updated_at").defaultTo(db.fn.now());
      });
    }

    // System Prompts table
    if (!(await db.schema.hasTable("system_prompts"))) {
      await db.schema.createTable("system_prompts", (table) => {
        table.string("id").primary();
        table.string("name").notNullable();
        table.text("content").notNullable();
        table
          .enum("context", [
            "general",
            "technical",
            "creative",
            "support",
            "custom",
          ])
          .notNullable();
        table.boolean("is_active").defaultTo(true);
        table.timestamp("created_at").defaultTo(db.fn.now());
        table.timestamp("updated_at").defaultTo(db.fn.now());
        table.string("created_by").notNullable();
        table.foreign("created_by").references("users.id");
      });
    }

    // User Preferences table
    if (!(await db.schema.hasTable("user_preferences"))) {
      await db.schema.createTable("user_preferences", (table) => {
        table.string("id").primary();
        table.string("user_id").notNullable();
        table
          .enum("default_provider", [
            "openai",
            "claude",
            "gemini",
            "mistral",
            "groq",
          ])
          .defaultTo("openai");
        table.string("default_agent");
        table.boolean("enable_thinking_indicators").defaultTo(true);
        table.boolean("enable_code_highlighting").defaultTo(true);
        table.boolean("enable_notifications").defaultTo(true);
        table.boolean("enable_hitl").defaultTo(false);
        table.enum("theme", ["light", "dark", "system"]).defaultTo("system");
        table.string("language").defaultTo("en");
        table.string("system_prompt_id");
        table.json("favorite_agents").defaultTo(JSON.stringify([]));
        table.json("recent_knowledge_bases").defaultTo(JSON.stringify([]));
        table.timestamp("created_at").defaultTo(db.fn.now());
        table.timestamp("updated_at").defaultTo(db.fn.now());
        table.foreign("user_id").references("users.id");
        table.foreign("system_prompt_id").references("system_prompts.id");
        table.foreign("default_agent").references("agents.id");
      });
    }

    // Widgets table
    if (!(await db.schema.hasTable("widgets"))) {
      await db.schema.createTable("widgets", (table) => {
        table.string("id").primary();
        table.string("name").notNullable();
        table.enum("type", ["chat", "form", "search", "custom"]).notNullable();
        table.string("agent_id").notNullable();
        table.json("configuration").defaultTo(JSON.stringify({}));
        table.json("appearance").defaultTo(JSON.stringify({}));
        table.boolean("is_active").defaultTo(true);
        table.string("domain");
        table.string("created_by").notNullable();
        table.string("organization_id");
        table.timestamp("created_at").defaultTo(db.fn.now());
        table.timestamp("updated_at").defaultTo(db.fn.now());
        table.foreign("agent_id").references("agents.id");
        table.foreign("created_by").references("users.id");
      });
    }

    // Deployments table
    if (!(await db.schema.hasTable("deployments"))) {
      await db.schema.createTable("deployments", (table) => {
        table.string("id").primary();
        table.string("name").notNullable();
        table.enum("type", ["widget", "api", "webhook"]).notNullable();
        table.string("agent_id").notNullable();
        table.json("configuration").defaultTo(JSON.stringify({}));
        table
          .enum("status", ["active", "inactive", "error"])
          .defaultTo("inactive");
        table.string("url");
        table.string("api_key");
        table.json("metrics").defaultTo(JSON.stringify({}));
        table.string("created_by").notNullable();
        table.string("organization_id");
        table.timestamp("created_at").defaultTo(db.fn.now());
        table.timestamp("updated_at").defaultTo(db.fn.now());
        table.foreign("agent_id").references("agents.id");
        table.foreign("created_by").references("users.id");
      });
    }

    // Analytics table
    if (!(await db.schema.hasTable("analytics"))) {
      await db.schema.createTable("analytics", (table) => {
        table.string("id").primary();
        table
          .enum("type", [
            "conversation",
            "agent",
            "tool",
            "knowledge_base",
            "widget",
          ])
          .notNullable();
        table.string("entity_id").notNullable();
        table.string("event").notNullable();
        table.json("data").defaultTo(JSON.stringify({}));
        table.string("user_id");
        table.string("session_id");
        table.string("organization_id");
        table.timestamp("timestamp").defaultTo(db.fn.now());
        table.foreign("user_id").references("users.id");
      });
    }

    // Audit Logs table
    if (!(await db.schema.hasTable("audit_logs"))) {
      await db.schema.createTable("audit_logs", (table) => {
        table.string("id").primary();
        table.string("user_id");
        table.string("action").notNullable();
        table.string("resource").notNullable();
        table.json("details");
        table.string("ip_address");
        table.string("user_agent");
        table.timestamp("timestamp").defaultTo(db.fn.now());
        table.foreign("user_id").references("users.id");
      });
    }

    logger.info("Database initialized successfully");
  } catch (error) {
    logger.error("Database initialization failed:", error);
    process.exit(1);
  }
}

// Environment variables
const JWT_SECRET =
  process.env.JWT_SECRET || "your-super-secret-jwt-key-change-in-production";
const ENCRYPTION_KEY =
  process.env.ENCRYPTION_KEY || crypto.randomBytes(32).toString("hex");
const PORT = process.env.PORT || 3001;
const NODE_ENV = process.env.NODE_ENV || "development";

// Session management with Redis
class SessionManager {
  static async createSession(userId, sessionData) {
    const sessionId = uuidv4();
    const sessionKey = `session:${sessionId}`;
    await redis.setex(
      sessionKey,
      3600,
      JSON.stringify({ userId, ...sessionData }),
    );
    return sessionId;
  }

  static async getSession(sessionId) {
    const sessionKey = `session:${sessionId}`;
    const sessionData = await redis.get(sessionKey);
    return sessionData ? JSON.parse(sessionData) : null;
  }

  static async updateSession(sessionId, updates) {
    const sessionKey = `session:${sessionId}`;
    const existing = await this.getSession(sessionId);
    if (existing) {
      await redis.setex(
        sessionKey,
        3600,
        JSON.stringify({ ...existing, ...updates }),
      );
    }
  }

  static async deleteSession(sessionId) {
    const sessionKey = `session:${sessionId}`;
    await redis.del(sessionKey);
  }

  static async getUserSessions(userId) {
    const keys = await redis.keys(`session:*`);
    const sessions = [];
    for (const key of keys) {
      const data = await redis.get(key);
      if (data) {
        const session = JSON.parse(data);
        if (session.userId === userId) {
          sessions.push({ id: key.replace("session:", ""), ...session });
        }
      }
    }
    return sessions;
  }
}

// UAUI Core Engine
class UAUICore {
  constructor() {
    this.providers = new Map();
    this.tools = new Map();
    this.agents = new Map();
    this.eventBus = new EventBus();
    this.stateManager = new StateManager();
    this.initializeProviders();
  }

  async initializeProviders() {
    try {
      // Load active API keys and initialize providers
      const apiKeys = await db("api_keys").where({ status: "active" });

      for (const keyRecord of apiKeys) {
        try {
          const decryptedKey = decrypt(keyRecord.encrypted_key);
          this.providers.set(keyRecord.provider, {
            id: keyRecord.provider,
            name: keyRecord.name,
            apiKey: decryptedKey,
            lastUsed: keyRecord.last_used,
          });
          logger.info(`Initialized provider: ${keyRecord.provider}`);
        } catch (error) {
          logger.error(
            `Failed to initialize provider ${keyRecord.provider}:`,
            error,
          );
        }
      }
    } catch (error) {
      logger.error("Failed to initialize providers:", error);
    }
  }

  async processRequest(request) {
    const { userId, sessionId, message, appType, metadata } = request;

    try {
      // Get session context
      let session = await SessionManager.getSession(sessionId);
      if (!session) {
        session = {
          userId,
          conversationId: metadata?.conversationId,
          agentId: metadata?.agentId,
          provider: metadata?.provider,
          context: {},
          createdAt: new Date(),
        };
        await SessionManager.createSession(sessionId, session);
      }

      // Select appropriate provider and agent
      const provider = await this.selectProvider(message, session);
      const agent = await this.selectAgent(message, session);

      // Emit processing start event
      this.eventBus.emit("processing_start", {
        sessionId,
        userId,
        message,
        provider: provider.id,
        timestamp: new Date(),
      });

      // Process with AI
      const response = await this.processWithAI({
        message,
        provider,
        agent,
        session,
        metadata,
      });

      // Update session state
      await SessionManager.updateSession(sessionId, {
        lastMessage: message,
        lastResponse: response.final,
        provider: provider.id,
        agentId: agent?.id,
        messageCount: (session.messageCount || 0) + 1,
      });

      // Emit processing complete event
      this.eventBus.emit("processing_complete", {
        sessionId,
        userId,
        response: response.final,
        provider: provider.id,
        timestamp: new Date(),
      });

      return response;
    } catch (error) {
      logger.error("UAUI processing error:", error);

      this.eventBus.emit("processing_error", {
        sessionId,
        userId,
        error: error.message,
        timestamp: new Date(),
      });

      return {
        error: error.message,
        final:
          "I apologize, but I encountered an error processing your request.",
      };
    }
  }

  async selectProvider(message, session) {
    // Smart provider selection based on routing rules and context
    const rules = await db("routing_rules")
      .where({ is_active: true })
      .orderBy("priority");

    for (const rule of rules) {
      if (this.evaluateCondition(rule.condition, message)) {
        const provider = this.providers.get(rule.provider);
        if (provider) {
          return provider;
        }
      }
    }

    // Fallback to first available provider
    const availableProvider = Array.from(this.providers.values())[0];
    return availableProvider || { id: "openai", name: "openai", apiKey: null };
  }

  async selectAgent(message, session) {
    // Agent selection logic
    if (session?.agentId) {
      const agent = await db("agents")
        .where({ id: session.agentId, is_active: true })
        .first();
      if (agent) return agent;
    }

    return null;
  }

  evaluateCondition(condition, message) {
    // Simple condition evaluation - can be extended
    if (condition === "default") return true;
    if (condition.includes("contains(")) {
      const match = condition.match(/contains\((.+)\)/);
      if (match) {
        const keywords = match[1]
          .split(",")
          .map((k) => k.trim().replace(/["']/g, ""));
        return keywords.some((keyword) =>
          message.toLowerCase().includes(keyword.toLowerCase()),
        );
      }
    }
    return false;
  }

  async processWithAI({ message, provider, agent, session, metadata }) {
    try {
      if (!provider.apiKey) {
        throw new Error(`No API key configured for provider ${provider.id}`);
      }

      // Build conversation history
      const conversationHistory = await this.buildConversationHistory(
        session.conversationId || metadata?.conversationId,
      );

      // Add system prompt if agent is specified
      const messages = [];
      if (agent && agent.system_prompt) {
        messages.push({ role: "system", content: agent.system_prompt });
      }

      // Add conversation history
      messages.push(...conversationHistory);

      // Add current message
      messages.push({ role: "user", content: message });

      // Process with the appropriate provider
      const response = await this.callProviderAPI(provider, messages, {
        temperature: agent?.temperature || 0.7,
        maxTokens: agent?.max_tokens || 2000,
      });

      // Update provider last used
      await db("api_keys")
        .where({ provider: provider.id })
        .update({ last_used: new Date() });

      return {
        stream: false,
        final: response.content,
        tool_call: null,
        state_update: {
          provider: provider.id,
          tokensUsed: response.usage?.totalTokens || 0,
        },
      };
    } catch (error) {
      logger.error(`Provider ${provider.id} processing error:`, error);
      throw error;
    }
  }

  async buildConversationHistory(conversationId) {
    if (!conversationId) return [];

    try {
      const messages = await db("messages")
        .where({ conversation_id: conversationId })
        .orderBy("timestamp", "asc")
        .limit(20); // Limit to last 20 messages

      return messages.map((msg) => ({
        role: msg.role,
        content: msg.content,
      }));
    } catch (error) {
      logger.error("Failed to build conversation history:", error);
      return [];
    }
  }

  async callProviderAPI(provider, messages, options = {}) {
    const { temperature = 0.7, maxTokens = 2000 } = options;

    switch (provider.id) {
      case "openai":
        return this.callOpenAI(provider.apiKey, messages, {
          temperature,
          maxTokens,
        });
      case "claude":
        return this.callClaude(provider.apiKey, messages, {
          temperature,
          maxTokens,
        });
      case "gemini":
        return this.callGemini(provider.apiKey, messages, {
          temperature,
          maxTokens,
        });
      case "mistral":
        return this.callMistral(provider.apiKey, messages, {
          temperature,
          maxTokens,
        });
      case "groq":
        return this.callGroq(provider.apiKey, messages, {
          temperature,
          maxTokens,
        });
      default:
        throw new Error(`Unsupported provider: ${provider.id}`);
    }
  }

  async callOpenAI(apiKey, messages, options) {
    const response = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${apiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: "gpt-4",
        messages,
        temperature: options.temperature,
        max_tokens: options.maxTokens,
      }),
    });

    if (!response.ok) {
      throw new Error(
        `OpenAI API error: ${response.status} ${response.statusText}`,
      );
    }

    const data = await response.json();
    return {
      content: data.choices[0]?.message?.content || "No response",
      usage: data.usage,
    };
  }

  async callClaude(apiKey, messages, options) {
    const response = await fetch("https://api.anthropic.com/v1/messages", {
      method: "POST",
      headers: {
        "x-api-key": apiKey,
        "Content-Type": "application/json",
        "anthropic-version": "2023-06-01",
      },
      body: JSON.stringify({
        model: "claude-3-sonnet-20240229",
        messages: messages.filter((m) => m.role !== "system"),
        system: messages.find((m) => m.role === "system")?.content,
        max_tokens: options.maxTokens,
        temperature: options.temperature,
      }),
    });

    if (!response.ok) {
      throw new Error(
        `Claude API error: ${response.status} ${response.statusText}`,
      );
    }

    const data = await response.json();
    return {
      content: data.content[0]?.text || "No response",
      usage: data.usage,
    };
  }

  async callGemini(apiKey, messages, options) {
    const response = await fetch(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${apiKey}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          contents: messages
            .filter((m) => m.role !== "system")
            .map((m) => ({
              parts: [{ text: m.content }],
              role: m.role === "assistant" ? "model" : "user",
            })),
          generationConfig: {
            temperature: options.temperature,
            maxOutputTokens: options.maxTokens,
          },
        }),
      },
    );

    if (!response.ok) {
      throw new Error(
        `Gemini API error: ${response.status} ${response.statusText}`,
      );
    }

    const data = await response.json();
    return {
      content: data.candidates[0]?.content?.parts[0]?.text || "No response",
      usage: data.usageMetadata,
    };
  }

  async callMistral(apiKey, messages, options) {
    const response = await fetch("https://api.mistral.ai/v1/chat/completions", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${apiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: "mistral-medium",
        messages,
        temperature: options.temperature,
        max_tokens: options.maxTokens,
      }),
    });

    if (!response.ok) {
      throw new Error(
        `Mistral API error: ${response.status} ${response.statusText}`,
      );
    }

    const data = await response.json();
    return {
      content: data.choices[0]?.message?.content || "No response",
      usage: data.usage,
    };
  }

  async callGroq(apiKey, messages, options) {
    const response = await fetch(
      "https://api.groq.com/openai/v1/chat/completions",
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${apiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          model: "mixtral-8x7b-32768",
          messages,
          temperature: options.temperature,
          max_tokens: options.maxTokens,
        }),
      },
    );

    if (!response.ok) {
      throw new Error(
        `Groq API error: ${response.status} ${response.statusText}`,
      );
    }

    const data = await response.json();
    return {
      content: data.choices[0]?.message?.content || "No response",
      usage: data.usage,
    };
  }
}

// Event Bus for real-time communication
class EventBus {
  constructor() {
    this.listeners = new Map();
  }

  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  emit(event, data) {
    const callbacks = this.listeners.get(event) || [];
    callbacks.forEach((callback) => {
      try {
        callback(data);
      } catch (error) {
        logger.error(`Event handler error for ${event}:`, error);
      }
    });
  }

  off(event, callback) {
    const callbacks = this.listeners.get(event) || [];
    const index = callbacks.indexOf(callback);
    if (index > -1) {
      callbacks.splice(index, 1);
    }
  }
}

// State Manager for cross-app state sync
class StateManager {
  constructor() {
    this.state = new Map();
  }

  async setState(key, value) {
    this.state.set(key, value);
    await redis.setex(`state:${key}`, 3600, JSON.stringify(value));
  }

  async getState(key) {
    if (this.state.has(key)) {
      return this.state.get(key);
    }

    const value = await redis.get(`state:${key}`);
    if (value) {
      const parsed = JSON.parse(value);
      this.state.set(key, parsed);
      return parsed;
    }

    return null;
  }

  async syncState(fromAppId, toAppId, state) {
    await this.setState(`${toAppId}:sync`, {
      from: fromAppId,
      state,
      timestamp: Date.now(),
    });
  }
}

// Initialize UAUI Core
const uauiCore = new UAUICore();

// Encryption utilities
function encrypt(text) {
  const cipher = crypto.createCipher("aes-256-cbc", ENCRYPTION_KEY);
  let encrypted = cipher.update(text, "utf8", "hex");
  encrypted += cipher.final("hex");
  return encrypted;
}

function decrypt(encryptedText) {
  const decipher = crypto.createDecipher("aes-256-cbc", ENCRYPTION_KEY);
  let decrypted = decipher.update(encryptedText, "hex", "utf8");
  decrypted += decipher.final("utf8");
  return decrypted;
}

// JWT utilities
function generateToken(user) {
  return jwt.sign(
    {
      sub: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
      permissions: user.permissions,
      organizationId: user.organization_id,
      createdAt: user.created_at,
    },
    JWT_SECRET,
    { expiresIn: "24h" },
  );
}

function verifyToken(token) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
}

// Middleware
function authenticateToken(req, res, next) {
  const authHeader = req.headers["authorization"];
  const token = authHeader && authHeader.split(" ")[1];

  if (!token) {
    return res.status(401).json({ error: "Access token required" });
  }

  const decoded = verifyToken(token);
  if (!decoded) {
    return res.status(403).json({ error: "Invalid or expired token" });
  }

  req.user = decoded;
  next();
}

function requireAdmin(req, res, next) {
  if (req.user.role !== "admin") {
    return res.status(403).json({ error: "Admin privileges required" });
  }
  next();
}

function requirePermission(permission) {
  return (req, res, next) => {
    if (
      !req.user.permissions.includes(permission) &&
      req.user.role !== "admin"
    ) {
      return res
        .status(403)
        .json({ error: `Permission required: ${permission}` });
    }
    next();
  };
}

// Audit logging
async function logActivity(userId, action, resource, details, req) {
  try {
    await db("audit_logs").insert({
      id: uuidv4(),
      user_id: userId,
      action,
      resource,
      details: JSON.stringify(details),
      ip_address: req.ip,
      user_agent: req.get("User-Agent"),
      timestamp: new Date(),
    });
  } catch (error) {
    logger.error("Failed to log activity:", error);
  }
}

// Express app setup
const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:5173",
    methods: ["GET", "POST"],
  },
});

// Security middleware
app.use(helmet());
app.use(
  cors({
    origin: process.env.FRONTEND_URL || "http://localhost:5173",
    credentials: true,
  }),
);

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: "Too many requests from this IP, please try again later.",
});
app.use(limiter);

// Body parsing
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true }));

// File upload configuration
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = "uploads/";
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    cb(
      null,
      file.fieldname + "-" + uniqueSuffix + path.extname(file.originalname),
    );
  },
});

const upload = multer({
  storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif|pdf|txt|doc|docx/;
    const extname = allowedTypes.test(
      path.extname(file.originalname).toLowerCase(),
    );
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error("Invalid file type"));
    }
  },
});

// Authentication routes
app.post(
  "/auth/register",
  [
    body("email").isEmail().normalizeEmail(),
    body("password").isLength({ min: 8 }),
    body("name").trim().isLength({ min: 2 }),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { email, password, name } = req.body;

      // Check if user exists
      const existingUser = await db("users").where({ email }).first();
      if (existingUser) {
        return res.status(400).json({ error: "User already exists" });
      }

      // Hash password
      const passwordHash = await bcrypt.hash(password, 12);

      // Create user
      const userId = uuidv4();
      const user = {
        id: userId,
        email,
        name,
        password_hash: passwordHash,
        role: "user",
        permissions: JSON.stringify([]),
        created_at: new Date(),
        last_active: new Date(),
      };

      await db("users").insert(user);

      // Create default preferences
      await db("user_preferences").insert({
        id: uuidv4(),
        user_id: userId,
        default_provider: "openai",
        enable_thinking_indicators: true,
        enable_code_highlighting: true,
        enable_notifications: true,
        enable_hitl: false,
        theme: "system",
        language: "en",
        favorite_agents: JSON.stringify([]),
        recent_knowledge_bases: JSON.stringify([]),
        created_at: new Date(),
        updated_at: new Date(),
      });

      const token = generateToken(user);

      await logActivity(userId, "register", "user", { email }, req);

      res.status(201).json({
        token,
        user: {
          id: userId,
          email,
          name,
          role: "user",
          permissions: [],
        },
      });
    } catch (error) {
      logger.error("Registration error:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  },
);

app.post(
  "/auth/login",
  [body("email").isEmail().normalizeEmail(), body("password").notEmpty()],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { email, password } = req.body;

      const user = await db("users").where({ email }).first();
      if (!user) {
        return res.status(401).json({ error: "Invalid credentials" });
      }

      const isValidPassword = await bcrypt.compare(
        password,
        user.password_hash,
      );
      if (!isValidPassword) {
        return res.status(401).json({ error: "Invalid credentials" });
      }

      // Update last active
      await db("users")
        .where({ id: user.id })
        .update({ last_active: new Date() });

      const token = generateToken(user);

      await logActivity(user.id, "login", "user", { email }, req);

      res.json({
        token,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          permissions: JSON.parse(user.permissions || "[]"),
          organizationId: user.organization_id,
        },
      });
    } catch (error) {
      logger.error("Login error:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  },
);

app.post("/auth/refresh", authenticateToken, async (req, res) => {
  try {
    const user = await db("users").where({ id: req.user.sub }).first();
    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    const token = generateToken(user);
    res.json({ token });
  } catch (error) {
    logger.error("Token refresh error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

app.post("/auth/logout", authenticateToken, async (req, res) => {
  try {
    await logActivity(req.user.sub, "logout", "user", {}, req);
    res.json({ message: "Logged out successfully" });
  } catch (error) {
    logger.error("Logout error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

// Agent management routes
app.get("/api/agents", authenticateToken, async (req, res) => {
  try {
    const agents = await db("agents")
      .select("*")
      .where(function () {
        this.where({ created_by: req.user.sub }).orWhere({ is_public: true });
      })
      .orderBy("created_at", "desc");

    res.json({ agents });
  } catch (error) {
    logger.error("Get agents error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

app.get("/api/agents/public", async (req, res) => {
  try {
    const agents = await db("agents")
      .select("*")
      .where({ is_public: true, is_active: true })
      .orderBy("created_at", "desc");

    res.json({ agents });
  } catch (error) {
    logger.error("Get public agents error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

app.post(
  "/api/agents",
  authenticateToken,
  requirePermission("create_agent"),
  [
    body("name").trim().isLength({ min: 1 }),
    body("description").optional().trim(),
    body("system_prompt").trim().isLength({ min: 1 }),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const agentId = uuidv4();
      const agent = {
        id: agentId,
        name: req.body.name,
        description: req.body.description || "",
        system_prompt: req.body.system_prompt,
        model: req.body.model || "gpt-4",
        temperature: req.body.temperature || 0.7,
        max_tokens: req.body.max_tokens || 2000,
        tools: JSON.stringify(req.body.tools || []),
        knowledge_bases: JSON.stringify(req.body.knowledge_bases || []),
        is_public: req.body.is_public || false,
        is_active: true,
        created_by: req.user.sub,
        organization_id: req.user.organizationId,
        metadata: JSON.stringify(req.body.metadata || {}),
        created_at: new Date(),
        updated_at: new Date(),
      };

      await db("agents").insert(agent);

      await logActivity(
        req.user.sub,
        "create",
        "agent",
        { agentId, name: agent.name },
        req,
      );

      res.status(201).json(agent);
    } catch (error) {
      logger.error("Create agent error:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  },
);

// Tool management routes
app.get("/api/tools", authenticateToken, async (req, res) => {
  try {
    const tools = await db("tools")
      .select("*")
      .where({ created_by: req.user.sub })
      .orderBy("created_at", "desc");

    res.json({ tools });
  } catch (error) {
    logger.error("Get tools error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

app.post(
  "/api/tools",
  authenticateToken,
  requirePermission("create_tool"),
  [
    body("name").trim().isLength({ min: 1 }),
    body("type").isIn(["function", "api", "webhook", "database", "custom"]),
    body("schema").isObject(),
    body("configuration").isObject(),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const toolId = uuidv4();
      const tool = {
        id: toolId,
        name: req.body.name,
        description: req.body.description || "",
        type: req.body.type,
        schema: JSON.stringify(req.body.schema),
        configuration: JSON.stringify(req.body.configuration),
        is_active: true,
        created_by: req.user.sub,
        organization_id: req.user.organizationId,
        created_at: new Date(),
        updated_at: new Date(),
      };

      await db("tools").insert(tool);

      await logActivity(
        req.user.sub,
        "create",
        "tool",
        { toolId, name: tool.name },
        req,
      );

      res.status(201).json(tool);
    } catch (error) {
      logger.error("Create tool error:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  },
);

// Knowledge Base management routes
app.get("/api/knowledge-bases", authenticateToken, async (req, res) => {
  try {
    const knowledgeBases = await db("knowledge_bases")
      .select("*")
      .where({ created_by: req.user.sub })
      .orderBy("created_at", "desc");

    res.json({ knowledgeBases });
  } catch (error) {
    logger.error("Get knowledge bases error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

app.post(
  "/api/knowledge-bases",
  authenticateToken,
  requirePermission("create_knowledge_base"),
  [
    body("name").trim().isLength({ min: 1 }),
    body("type").isIn(["documents", "qa", "structured", "web"]),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const kbId = uuidv4();
      const knowledgeBase = {
        id: kbId,
        name: req.body.name,
        description: req.body.description || "",
        type: req.body.type,
        status: "inactive",
        documents_count: 0,
        settings: JSON.stringify(req.body.settings || {}),
        created_by: req.user.sub,
        organization_id: req.user.organizationId,
        created_at: new Date(),
        updated_at: new Date(),
      };

      await db("knowledge_bases").insert(knowledgeBase);

      await logActivity(
        req.user.sub,
        "create",
        "knowledge_base",
        { kbId, name: knowledgeBase.name },
        req,
      );

      res.status(201).json(knowledgeBase);
    } catch (error) {
      logger.error("Create knowledge base error:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  },
);

// Widget management routes
app.get("/api/widgets", authenticateToken, async (req, res) => {
  try {
    const widgets = await db("widgets")
      .select("*")
      .where({ created_by: req.user.sub })
      .orderBy("created_at", "desc");

    res.json({ widgets });
  } catch (error) {
    logger.error("Get widgets error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

app.post(
  "/api/widgets",
  authenticateToken,
  requirePermission("create_widget"),
  [
    body("name").trim().isLength({ min: 1 }),
    body("type").isIn(["chat", "form", "search", "custom"]),
    body("agent_id").isUUID(),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const widgetId = uuidv4();
      const widget = {
        id: widgetId,
        name: req.body.name,
        type: req.body.type,
        agent_id: req.body.agent_id,
        configuration: JSON.stringify(req.body.configuration || {}),
        appearance: JSON.stringify(req.body.appearance || {}),
        is_active: true,
        domain: req.body.domain,
        created_by: req.user.sub,
        organization_id: req.user.organizationId,
        created_at: new Date(),
        updated_at: new Date(),
      };

      await db("widgets").insert(widget);

      await logActivity(
        req.user.sub,
        "create",
        "widget",
        { widgetId, name: widget.name },
        req,
      );

      res.status(201).json(widget);
    } catch (error) {
      logger.error("Create widget error:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  },
);

// HITL management routes
app.get("/api/hitl-requests", authenticateToken, async (req, res) => {
  try {
    const requests = await db("hitl_requests")
      .select("*")
      .where(function () {
        if (req.user.role === "admin") {
          // Admins see all requests
          this.where({});
        } else {
          // Users see requests assigned to them or unassigned
          this.where({ assigned_to: req.user.sub }).orWhereNull("assigned_to");
        }
      })
      .orderBy("created_at", "desc");

    res.json({ requests });
  } catch (error) {
    logger.error("Get HITL requests error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

app.post(
  "/api/hitl-requests/:id/respond",
  authenticateToken,
  [body("response").trim().isLength({ min: 1 })],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { id } = req.params;
      const { response, status } = req.body;

      const updated = await db("hitl_requests")
        .where({ id })
        .update({
          response,
          status: status || "completed",
          resolved_at: new Date(),
          updated_at: new Date(),
        });

      if (!updated) {
        return res.status(404).json({ error: "HITL request not found" });
      }

      await logActivity(
        req.user.sub,
        "respond",
        "hitl_request",
        { id, response },
        req,
      );

      res.json({ message: "Response submitted successfully" });
    } catch (error) {
      logger.error("HITL response error:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  },
);

// Analytics routes
app.get(
  "/api/analytics/overview",
  authenticateToken,
  requireAdmin,
  async (req, res) => {
    try {
      const [userCount] = await db("users").count("* as count");
      const [conversationCount] = await db("conversations").count("* as count");
      const [messageCount] = await db("messages").count("* as count");
      const [agentCount] = await db("agents").count("* as count");
      const [toolCount] = await db("tools").count("* as count");
      const [kbCount] = await db("knowledge_bases").count("* as count");

      const recentActivity = await db("audit_logs")
        .select("action", "resource", "timestamp")
        .orderBy("timestamp", "desc")
        .limit(10);

      const providerUsage = await db("messages")
        .select("provider")
        .count("* as count")
        .whereNotNull("provider")
        .groupBy("provider");

      res.json({
        users: userCount.count,
        conversations: conversationCount.count,
        messages: messageCount.count,
        agents: agentCount.count,
        tools: toolCount.count,
        knowledgeBases: kbCount.count,
        recentActivity,
        providerUsage,
      });
    } catch (error) {
      logger.error("Get analytics error:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  },
);

// WebSocket authentication middleware
io.use(async (socket, next) => {
  try {
    const token = socket.handshake.auth.token;
    if (!token) {
      return next(new Error("Authentication token required"));
    }

    const decoded = verifyToken(token);
    if (!decoded) {
      return next(new Error("Invalid or expired token"));
    }

    const user = await db("users").where({ id: decoded.sub }).first();
    if (!user) {
      return next(new Error("User not found"));
    }

    socket.userId = user.id;
    socket.userRole = user.role;
    socket.organizationId = user.organization_id;
    next();
  } catch (error) {
    logger.error("WebSocket authentication error:", error);
    next(new Error("Authentication failed"));
  }
});

// WebSocket APIX Protocol implementation
io.on("connection", (socket) => {
  logger.info(`User connected: ${socket.userId}`);

  // Create session for this connection
  const sessionId = uuidv4();
  SessionManager.createSession(socket.userId, {
    socketId: socket.id,
    connectedAt: new Date(),
    organizationId: socket.organizationId,
  });

  socket.on("apix:send_message", async (data) => {
    try {
      const {
        conversationId,
        content,
        provider = "openai",
        agentId,
      } = data.payload;

      // Save user message
      const messageId = uuidv4();
      await db("messages").insert({
        id: messageId,
        conversation_id: conversationId,
        role: "user",
        content,
        timestamp: new Date(),
        status: "complete",
      });

      // Send thinking indicator
      socket.emit("apix:thinking", {
        type: "thinking",
        data: { conversationId, status: "processing", isThinking: true },
      });

      // Process with UAUI Core
      const response = await uauiCore.processRequest({
        userId: socket.userId,
        sessionId,
        message: content,
        appType: "dashboard",
        metadata: { conversationId, provider, agentId },
      });

      // Save assistant response
      const assistantMessageId = uuidv4();
      await db("messages").insert({
        id: assistantMessageId,
        conversation_id: conversationId,
        role: "assistant",
        content:
          response.final || "I apologize, but I couldn't process your request.",
        timestamp: new Date(),
        status: "complete",
        provider,
      });

      // Send complete response
      socket.emit("apix:complete", {
        type: "message",
        data: {
          conversationId,
          messageId: assistantMessageId,
          content: response.final,
          provider,
        },
      });

      // Update conversation
      await db("conversations")
        .where({ id: conversationId })
        .update({
          updated_at: new Date(),
          last_activity: new Date(),
          message_count: db.raw("message_count + 2"),
        });
    } catch (error) {
      logger.error("Message processing error:", error);
      socket.emit("apix:error", {
        type: "error",
        data: { message: error.message },
      });
    }
  });

  socket.on("apix:submit_form", async (data) => {
    try {
      const { conversationId, formId, formData } = data.payload;

      // Process form submission
      socket.emit("apix:message", {
        type: "message",
        data: {
          conversationId,
          content: `Form submitted successfully: ${JSON.stringify(formData)}`,
          isSystem: true,
        },
      });
    } catch (error) {
      logger.error("Form submission error:", error);
      socket.emit("apix:error", {
        type: "error",
        data: { message: error.message },
      });
    }
  });

  socket.on("apix:request_hitl", async (data) => {
    try {
      const { conversationId, messageId, type, title, description, context } =
        data.payload;

      const hitlId = uuidv4();
      await db("hitl_requests").insert({
        id: hitlId,
        conversation_id: conversationId,
        message_id: messageId,
        type,
        title,
        description,
        context: JSON.stringify(context || {}),
        status: "pending",
        metadata: JSON.stringify({}),
        created_at: new Date(),
        updated_at: new Date(),
      });

      // Notify admins about HITL request
      socket.broadcast.emit("apix:hitl_request", {
        type: "hitl_request",
        data: {
          conversationId,
          request: {
            id: hitlId,
            type,
            title,
            description,
            status: "pending",
          },
        },
      });

      socket.emit("apix:message", {
        type: "message",
        data: {
          conversationId,
          content:
            "Your request has been forwarded to a human assistant. Please wait for a response.",
          isSystem: true,
        },
      });
    } catch (error) {
      logger.error("HITL request error:", error);
      socket.emit("apix:error", {
        type: "error",
        data: { message: error.message },
      });
    }
  });

  socket.on("disconnect", async () => {
    logger.info(`User disconnected: ${socket.userId}`);
    await SessionManager.deleteSession(sessionId);
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  logger.error("Unhandled error:", error);
  res.status(500).json({ error: "Internal server error" });
});

// Initialize and start server
async function startServer() {
  try {
    await initializeDatabase();

    // Test Redis connection
    await redis.ping();
    logger.info("Redis connected successfully");

    server.listen(PORT, () => {
      logger.info(`SynapseAI Server running on port ${PORT}`);
      logger.info(`Environment: ${NODE_ENV}`);
      logger.info(`WebSocket endpoint: /apix`);
    });
  } catch (error) {
    logger.error("Failed to start server:", error);
    process.exit(1);
  }
}

startServer();
