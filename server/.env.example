# Server Configuration
PORT=3001
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production

# Encryption Key for API Keys
ENCRYPTION_KEY=your-32-character-encryption-key-here

# Frontend URL for CORS
FRONTEND_URL=http://localhost:5173

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Database Configuration (SQLite by default)
DATABASE_URL=./database.sqlite

# AI Provider API Keys (will be encrypted and stored in database)
# These are examples - actual keys should be added through the admin panel
# OPENAI_API_KEY=sk-...
# CLAUDE_API_KEY=sk-ant-...
# GEMINI_API_KEY=AIza...
# MISTRAL_API_KEY=...
# GROQ_API_KEY=gsk_...
